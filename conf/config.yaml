neo4j:
  schema: bolt
  host: !env ${NEO4J_HOST|yftool-db-neo4j}
  port: !env ${NEO4J_PORT|7687}
  username: !env ${NEO4J_USERNAME|neo4j}
  password: !env ${NEO4J_PASSWORD|yunfu2017}
nebula:
  host: !env ${NEBULA_HOST|*************}
  port: !env ${NEBULA_PORT|9669}
  username: !env ${NEBULA_USERNAME|root}
  password: !env ${NEBULA_PASSWORD|root}
es:
  entity_index: kg_nodes
  host: !env ${ES_HOST|http://yftool-db-elasticsearch:9200}
  base: ""
  num_threads: 10
  stat_conf:
    host: yftool-ops-statsd
    port: 9125
    prefix: ""
resource_log:
  num_threads: 5
  stat_conf:
    host: yftool-ops-statsd
    port: 9125
    prefix: ""
es_analysis:
  analysis:
    analyzer:
      ik_searcher:
        type: custom
        tokenizer: ik_smart
        filter:
          - synonym_filter
          - stopwords_filter
    filter:
      synonym_filter:
        type: synonym
        synonyms_path: analysis/synonym.txt
        updateable: True
      stopwords_filter:
        type: stop
        stopwords_path: analysis/stopwords.txt
        updateable: True
stat_conf:
  host: yftool-ops-statsd
  port: 9125
  prefix: ""
candidate_generator:
  candidate_count: 5
  delete_attributes:
    [
      "create_time",
      "update_time",
      "_show_name",
      "_eid",
      "name",
      "_create_time",
      "_update_time",
      "_type",
    ]
fast_sim:
  idf_file: /opt/yunfu/yfproduct/yfkm/services/kg/fusion/data/idf.txt
  threshold: 0.5
alias_file: tests/fixtures/entity_alias.tsv
