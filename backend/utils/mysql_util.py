from typing import List

from tenacity import retry, stop_after_attempt, wait_fixed

from backend.fusion.schema import Entity
from backend.fusion.schema import EntityTriples as FusionCandidate
from backend.models import (
    Candidate,
    EntityAlign,
    FusionTriple,
    Kg,
    Source,
    TripleSource,
    UpdateTask,
)
from backend.utils.db_con_util import close_old_database_connections


class MysqlUtil:

    @retry(wait=wait_fixed(1), stop=stop_after_attempt(3))
    @close_old_database_connections
    @staticmethod
    def get_update_task_by_id(task_id: int) -> UpdateTask:
        update_task: UpdateTask = UpdateTask.objects.get(id=task_id)
        return update_task

    @retry(wait=wait_fixed(1), stop=stop_after_attempt(3))
    @close_old_database_connections
    @staticmethod
    def add_entity_align(entity: Entity, kg: str, score: float, fusion_type: str, task: UpdateTask,
                         status: str) -> EntityAlign:
        if fusion_type == 'ontology':
            node_type = EntityAlign.TypeChoices.ONTOLOGY
        else:
            node_type = EntityAlign.TypeChoices.ENTITY
        if status == 'merge':
            save_status = EntityAlign.FusionTypeChoices.MERGE
        else:
            save_status = EntityAlign.FusionTypeChoices.CREATE
        kg_id = int(kg[2:])
        kg_ = Kg.objects.get(id=kg_id)
        entity_align = EntityAlign(
            eid=entity.eid,
            name=entity.name,
            kg=kg_,
            type=entity.type,
            score=score,
            node_type=node_type,
            task=task,
            status=save_status
        )
        entity_align.save()
        return entity_align

    @retry(wait=wait_fixed(1), stop=stop_after_attempt(3))
    @close_old_database_connections
    @staticmethod
    def add_candidate(candidates: List[FusionCandidate], entity_align: EntityAlign) -> None:
        for candidate in candidates:
            candidate_mysql = Candidate(
                name=candidate.head.name,
                eid=candidate.head.eid,
                score=candidate.score,
                entity=entity_align
            )
            candidate_mysql.save()

    @retry(wait=wait_fixed(1), stop=stop_after_attempt(3))
    @close_old_database_connections
    @staticmethod
    def add_source(source_entity: Entity, kg: str, entity_align: EntityAlign) -> None:
        kg_id = kg[2:]
        kg_ = Kg.objects.get(id=kg_id)
        mysql_entity = Source(
            kg=kg_,
            eid=source_entity.eid,
            name=source_entity.name,
            entity=entity_align
        )
        mysql_entity.save()

    @close_old_database_connections
    @staticmethod
    def get_aligned_entity(source_entity: Entity, task_id: int) -> EntityAlign:
        source = Source.objects.filter(eid=source_entity.eid, entity__task__id=task_id).first()
        # 应该会有source为None的情况，但是在实际流程中，不可以为None
        aligned_entity: EntityAlign = source.entity
        return aligned_entity

    @retry(wait=wait_fixed(1), stop=stop_after_attempt(3))
    @close_old_database_connections
    @staticmethod
    def add_fusion_triple(triple: str, status: int, type_: int, task: UpdateTask, node_type: int) -> FusionTriple:
        # TODO: task可以单独取一次，没必要每次都取
        # TODO: 是否有必要做去重（因为只是展示，感觉意义不大）
        fusion_triple = FusionTriple(
            triple=triple,
            status=status,
            type=type_,
            task=task,
            node_type=node_type
        )
        fusion_triple.save()
        return fusion_triple

    @retry(wait=wait_fixed(1), stop=stop_after_attempt(3))
    @close_old_database_connections
    @staticmethod
    def add_source_triple(triple: str, kg_: Kg, fusion_triple: FusionTriple) -> None:
        # TODO: kg可以单独取一次，没必要每次都取
        source_triple = TripleSource.objects.filter(triple=triple, fusion_triple=fusion_triple).first()
        if source_triple:
            return
        source_triple = TripleSource(
            kg=kg_,
            triple=triple,
            fusion_triple=fusion_triple
        )
        source_triple.save()
