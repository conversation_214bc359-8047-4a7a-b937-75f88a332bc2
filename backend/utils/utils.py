import time
import uuid
from typing import Dict, List

from yunfu.common import DelayedBatchExecutor, StatClient

from backend.models import ResourceChangeLog
from backend.utils.db_con_util import close_old_database_connections


class UuidUtils:
    @classmethod
    def get_uuid(cls, text: str) -> str:
        now = time.time()
        return str(uuid.uuid3(uuid.NAMESPACE_DNS, f'{text}_{now}'))


class ResourceChangeLogExecutor(DelayedBatchExecutor):
    def __init__(self, conf: Dict) -> None:
        num_threads = conf.get('num_threads', 1)
        batch_size = conf.get('batch_size', 200)
        batch_size = conf.get('duration', 0.1)
        self.stat_client = None
        if conf.get('stat_conf') is not None:
            self.stat_client = StatClient(**conf['stat_conf'])
        super().__init__(num_threads=num_threads, batch_size=batch_size, duration=batch_size)

    @close_old_database_connections
    def run(self, tasks: List) -> None:
        begin_time = time.time()
        bulk_logs = []
        for task in tasks:
            resource_name = task.kwargs['resource_name']
            resource_id = task.kwargs['resource_id']
            resource_type = task.kwargs['resource_type']
            operation = task.kwargs['operation']
            value = task.kwargs['value']
            source_id = task.kwargs['source_id']
            detail = task.kwargs.get('detail')
            yfid_ = ResourceChangeLog.create_yfid(resource_name, resource_id, operation, value)
            if ResourceChangeLog.objects.filter(yfid=yfid_).exists():
                log = ResourceChangeLog.objects.filter(yfid=yfid_).first()
                if isinstance(source_id, list):
                    log.sources.extend(source_id)
                else:
                    log.sources.append(source_id)
                log.sources = list(set(log.sources))
                if detail:
                    log.detail = detail
                log.save()
            else:
                new_log = ResourceChangeLog()
                new_log.resource_name = resource_name
                new_log.resource_id = resource_id
                new_log.resource_type = resource_type
                new_log.operation = operation
                new_log.value = value
                new_log.yfid = yfid_
                new_log.sources = source_id if isinstance(source_id, list) else [source_id]
                new_log.detail = detail
                bulk_logs.append(new_log)
        if bulk_logs:
            ResourceChangeLog.objects.bulk_create(bulk_logs, 200)
        if self.stat_client is not None:
            self.stat_client.timing('yfproduct-yfkm-fusion',
                                    time.time() - begin_time,
                                    tags={'step': 'add_resource_tracer'})
