from typing import Dict, Iterator, Optional

from elasticsearch import Elasticsearch
from elasticsearch_dsl import Document, Q, Search, connections
from yunfu.common import ConfigUtils, yfid
from yunfu.db.nosql import EsClient

from backend.documents import SynonymsDictory
from backend.fusion.schema import Entity
from backend.utils import UuidUtils

config = ConfigUtils.load('conf/config.yaml')


class ESUtils:
    def __init__(self, conf: dict):
        self.conf = conf
        self.client = Elasticsearch(conf['es']['host'])
        connections.create_connection(hosts=[conf['es']['host']], timeout=60)
        self.index = conf['es']['entity_index']
        self.es_client = EsClient(config['es'])
        entity_searcher = Search(index=self.index, using=self.client)
        self.entity_searcher = entity_searcher.params(track_total_hits=True)
        self.synonym_searcher = Search(index=SynonymsDictory.Index.name, using=self.client)

    def get_synonyms_by_name(self, name: str, synonym_dictory_id: str) -> Optional[Document]:
        query = (Q('term', synonyms=name) &
                 Q('term', synonym_dictory_id=synonym_dictory_id) & Q('term', version='c'))
        synonyms = self.synonym_searcher.query(query).execute()
        if synonyms.hits.total.value > 0:
            return synonyms[0]
        return None

    def get_entities_by_name(self, name: str, kg: str, fusion_type: str) -> Iterator:
        if fusion_type == 'ontology':
            node_type = 'ont'
        else:
            node_type = 'entity'
        kg_id = int(kg.lstrip('KG'))
        name_query = Q('match', name=name)
        kg_query = Q('term', kg_id=kg_id)
        node_type_query = Q('term', node_type=node_type)
        query = name_query & kg_query & node_type_query & Q('term', version='c')
        entities: Iterator = self.entity_searcher.query(
            query)[:self.conf['candidate_generator']['candidate_count']].execute()
        return entities

    def get_entity_by_id(self, id_: str) -> Dict:
        query = Q('term', _id=id_)
        result: Dict = self.entity_searcher.query(query).execute().hits[0].to_dict()
        return result

    def update_entity(self, body: Dict, id_: str) -> Dict:
        result: Dict = self.client.update(index=self.index, id=id_, body=body, params={'refresh': 'true'})
        return result

    def _create_es_entity(self, entity: Entity, kg: str, fusion_type: str) -> None:
        kg_id = int(kg.lstrip('KG'))
        node_type = 'ont' if fusion_type == 'ontology' else 'entity'
        body = {
            'name': entity.name,
            'kg_id': int(kg_id),
            'eid': entity.eid,
            'node_type': node_type,
            'project': 'yfkm',
            'type': entity.type,
            'version': 'c',
            'status': 1,
            'source_type': 3
        }
        es_id = yfid(UuidUtils.get_uuid(entity.name))
        self.es_client.create(self.index, body, es_id, delayed_batch=True)
