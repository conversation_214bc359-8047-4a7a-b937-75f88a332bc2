from abc import ABC, abstractmethod
from typing import List, Optional

from yunfu.db.graph.models import Edge, Node

from backend.fusion.schema import Entity, EntityTriples


class BaseGraphMapper(ABC):

    @abstractmethod
    def get_node_by_eid(self, space: str, eid: str) -> Optional[Node]:
        """根据节点_eid获取节点

        :param space: 图空间名称
        :param eid: 节点_eid
        :return: 节点
        """
        raise NotImplementedError

    @abstractmethod
    def get_nodes_by_label(self, space: str, skip: int, limit: int) -> List[Node]:
        """根据标签获取节点列表

        :param space: 图空间名称
        :param skip: 跳过数量
        :param limit: 限制数量
        :return: 节点列表
        """
        raise NotImplementedError

    @abstractmethod
    def get_edges_by_head_eid(self, space: str, eid: str) -> List[Edge]:
        """根据head的_eid查关系

        :param space: 图空间名称
        :param eid: 头节点_eid
        :return: 边列表
        """
        raise NotImplementedError

    def create_entity(self, space: str, entity: EntityTriples) -> Node:
        attributes = entity.attributes
        props = {'name': entity.head.name, '_eid': entity.head.e_id, '_show_name': entity.head.name}
        for attribute in attributes:
            props[attribute.attribute_name] = attribute.attribute_value
        return Node(id=entity.head.e_id, types=[space], props=props)

    @abstractmethod
    def update_attributes(self, space: str, entity: EntityTriples) -> Node:
        """更新实体属性

        :param space: 图空间名称
        :param eid: 实体_eid
        :param attributes: 属性字典
        :return: 更新后的节点
        """
        raise NotImplementedError

    @abstractmethod
    def create_relation(self, space: str, head_eid: str, tail_eid: str, relation_name: str) -> Edge:
        """创建关系

        :param space: 图空间名称
        :param head_eid: 头节点_eid
        :param tail_eid: 尾节点_eid
        :param relation_name: 关系名称
        :return: 创建的边
        """
        raise NotImplementedError

    @abstractmethod
    def update_node_type(self, space: str) -> None:
        """更新节点类型

        :param space: 图空间名称
        """
        raise NotImplementedError

    def create_entity_without_properties(self, entity: Entity, space: str, fusion_type: str):
        types = [space, 'c']
        if fusion_type == 'ontology':
            types.append('concept')
        props = {'name': entity.name, '_eid': entity.e_id, '_show_name': entity.name}
        return Node(id=entity.e_id, types=types, props=props)
