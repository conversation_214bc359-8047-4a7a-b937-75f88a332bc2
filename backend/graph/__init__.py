from yunfu.common import ConfigUtils

from backend.models import GraphDbs

from .base_graph_mapper import BaseGraphMapper
from .nebula_graph_mapper import NebulaGraphMapper
from .neo4j_graph_mapper import Neo4jGraphMapper

config = ConfigUtils.load('conf/config.yaml')

__all__ = [
    "NebulaGraphMapper",
    "Neo4jGraphMapper",
    "BaseGraphMapper",
    "get_graph_mapper",
]


def get_graph_mapper(db: GraphDbs, enable_version: bool = True):
    """获取图数据库映射器"""
    if db == GraphDbs.NEBULA:
        return NebulaGraphMapper(config["neo4j"], enable_version)
    if db == GraphDbs.NEO4J:
        return Neo4jGraphMapper(config["nebula"], enable_version)
    raise ValueError(f"Unsupported db: {db}")
