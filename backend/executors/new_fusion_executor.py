from typing import Dict

from asgiref.sync import sync_to_async
from docarray import DocumentArray
from yfflow import Yf<PERSON>xecutor, requests

from backend.fusion.pipelines import (
    KnowledgeFusionPipeline,
    TripleFusionPipeline,
)
from backend.schemas import KnowledgeFusionParams, TripleFusionParams


class NewFusionExecutor(YfExecutor):

    @requests(on='/kgs/knowledge_fusion/')
    async def knowledge_fusion(self, docs: DocumentArray, **kwargs: Dict[str, dict]) -> DocumentArray:
        for doc in docs:
            params = KnowledgeFusionParams(**doc.tags)
            self.logger.info(f'[knowledge_fusion] {params=}')
            await sync_to_async(KnowledgeFusionPipeline.run)(params)

    @requests(on='/kgs/triple_fusion/')
    async def triple_fusion(self, docs: DocumentArray, **kwargs: Dict[str, dict]) -> DocumentArray:
        for doc in docs:
            params = TripleFusionParams(**doc.tags)
            self.logger.info(f'[triple_fusion] {params=}')
            if params.fusion_type not in ['ontology', 'entity']:
                continue
            await sync_to_async(TripleFusionPipeline.run)(params)
