from typing import Optional

from pydantic import BaseModel


class KnowledgeFusionParams(BaseModel):
    """知识融合参数模型"""
    task_id: int                                    # 更新任务ID
    fusion_type: str                                # 融合类型
    synonym_dictory_id: Optional[str] = None        # 同义词词典ID
    sim_threshold: Optional[float] = None           # 相似度阈值


class TripleFusionParams(BaseModel):
    """三元组融合参数模型"""
    task_id: int                                    # 更新任务ID
    fusion_type: Optional[str] = None               # 融合类型
