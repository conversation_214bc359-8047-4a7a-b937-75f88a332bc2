from typing import List, Optional

from backend.fusion.schema.relation import Relation


class RelationAlignment:

    def align(self, to_fusion_relation: Relation, base_entity_relations: List[Relation]) -> Optional[int]:
        # TODO: 讨论：是否存在一个关系名对齐多个关系名的情况，例如：属于对齐了属于、是
        # TODO: 接上文：可能原因是基础图谱没有做属性关系归一化
        relation_align_num = None
        for num, base_entity_relation in enumerate(base_entity_relations):
            if base_entity_relation.relation == to_fusion_relation.relation:
                relation_align_num = num
                # 如果关系相同仍需要往后查询，确认是否有关系尾实体相同的情况
                # 应对base图谱与to_fusion图谱具有多条相同关系存在的情况
                if base_entity_relation.tail.name == to_fusion_relation.tail.name:
                    return num
        return relation_align_num
