from backend.fusion.schema import Attribute


class AttributeValueAlignment:
    # TODO: add more alignment methods
    def align(self, attribute: Attribute, to_fusion_attribute: Attribute) -> Attribute:
        if attribute.attribute_value == to_fusion_attribute.attribute_value:
            return attribute
        # value = f'{attribute.attribute_value};{to_fusion_attribute.attribute_value}'
        value = f'{attribute.attribute_value}'
        return Attribute(attribute_name=attribute.attribute_name, attribute_value=value)
