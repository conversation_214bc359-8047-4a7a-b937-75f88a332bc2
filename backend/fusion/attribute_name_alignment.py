from typing import List, Optional

from backend.fusion.schema import Attribute


class AttributeNameAlignment:
    def align(self, to_fusion_attribute: Attribute, candidates: List[Attribute]) -> Optional[int]:
        for num, candidate in enumerate(candidates):
            # TODO: add more alignment methods
            if candidate.attribute_name == to_fusion_attribute.attribute_name:
                return num
        return None
