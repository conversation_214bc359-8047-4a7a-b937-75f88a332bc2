from typing import Dict, List, Optional

from yunfu.nlp.fastsim import FastSim

from backend.fusion.schema.entity_triple import EntityTriples


class EntityAlignment:

    def __init__(self, conf: Dict) -> None:
        self.fast_sim = FastSim(idf_file=conf['fast_sim']['idf_file'])
        self.fast_sim_threshold = conf['fast_sim']['threshold']

    def align(
        self, entity: EntityTriples, candidates: List[EntityTriples],
        synonym_candidates: Optional[List[EntityTriples]] = None, sim_threshold: Optional[float] = None
    ) -> Optional[List[Dict]]:
        return self._align_entity_by_similarity(entity, candidates, synonym_candidates, sim_threshold)

    def _align_entity_by_similarity(
        self, entity: EntityTriples, candidates: List[EntityTriples],
        synonym_candidates: Optional[List[EntityTriples]], sim_threshold: Optional[float] = None
    ) -> Optional[List[Dict]]:
        aligned_entities: List[Dict] = []
        sim_threshold = sim_threshold or self.fast_sim_threshold
        if not synonym_candidates:
            synonym_candidates = []
        for candidate in candidates:
            score = self.fast_sim.similarity(entity.head.name, candidate.head.name)
            candidate.score = round(score, 3)
            if score > sim_threshold:
                aligned_entities.append({'score': score, 'candidate': candidate})
        for candidate in synonym_candidates:
            candidate.score = 2.0
            aligned_entities.append({'score': 2.0, 'candidate': candidate})
        if aligned_entities:
            sorted_aligned_entities = sorted(aligned_entities, key=lambda x: x['score'], reverse=True)
            aligned_entity: List[Dict] = sorted_aligned_entities
            return aligned_entity
        return None
