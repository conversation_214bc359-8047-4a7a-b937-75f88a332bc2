import copy
from typing import Dict, List, Optional, Tuple

from yfflow import <PERSON><PERSON><PERSON>ogger
from yunfu.common import yfid
from yunfu.db.graph.deprecated.models import Node
from yunfu.db.graph.models import Edge

from backend.fusion.candidate_generator import CandidateGenerator
from backend.fusion.entity_alignment import EntityAlignment
from backend.fusion.relation_alignment import RelationAlignment
from backend.fusion.schema.entity import Entity
from backend.fusion.schema.relation import Relation
from backend.graph.neo4j_utils import Neo4jUtils
from backend.models import EntityAlign, FusionTriple, Kg, ResourceChangeLog, UpdateTask
from backend.utils import ResourceChangeLogExecutor
from backend.utils.db_con_util import close_old_database_connections
from backend.utils.es_utils import ESUtils
from backend.utils.mysql_util import MysqlUtil

from .attribute_name_alignment import AttributeNameAlignment
from .attribute_value_alignment import AttributeValueAlignment
from .schema import Entity<PERSON>riples

logger = YfLogger(__name__)


class KnowledgeFusion:
    def __init__(self, conf: Dict) -> None:
        self.conf = conf
        self.es_utils = ESUtils(conf)
        self.neo4j_utils = Neo4jUtils(conf)
        self.candidate_generator = CandidateGenerator(conf)
        self.entity_alignment = EntityAlignment(conf)
        self.mysql_utils = MysqlUtil()
        self.relation_alignment = RelationAlignment()
        self.attribute_name_alignment = AttributeNameAlignment()
        self.attribute_value_alignment = AttributeValueAlignment()
        logger.info(f'[conf]:{conf}')
        self.resource_change_log_executor = ResourceChangeLogExecutor(conf['resource_log'])

    def fuse(
        self, task: UpdateTask, fusion_type: str, synonym_dictory_id: Optional[str] = None,
        sim_threshold: Optional[float] = None
    ) -> None:
        """医科院的数据融合，区别是这个需要加到中间过程的控制及展示，通用的则没有

        :param base_kg: _description_
        :type base_kg: str
        :param to_fusion_kg: _description_
        :type to_fusion_kg: str
        """
        fusion_kg_id = task.update_kg.id
        base_kg_id = task.kg.id
        base_kg = f'KG{base_kg_id}'
        to_fusion_kg = f'KG{fusion_kg_id}'
        logger.info(f'[base_kg]:{base_kg}')
        logger.info(f'[fusion_kg]:{to_fusion_kg}')
        to_fuse_label = self._get_label(to_fusion_kg, fusion_type)
        skip = 0
        to_fusion_entities = self.neo4j_utils.get_nodes_by_label(to_fuse_label, skip, self.conf['neo4j']['limit'])
        while to_fusion_entities:
            for entity in to_fusion_entities:
                if fusion_type == 'entity' and 'concept' in entity.labels:  # 只能先以这个方法取实体
                    continue
                logger.info(f'{entity.properties["name"]}开始对齐')
                entity = Entity(eid=entity.properties['_eid'],
                                name=entity.properties['name'],
                                type=entity.properties.get('_type', '其他'))
                to_fusion_entity = self.candidate_generator.generate_candidate(entity, to_fusion_kg, fusion_type, False)
                _ = self._align_entity(
                    to_fusion_entity, base_kg, to_fusion_kg,
                    fusion_type, task, synonym_dictory_id, sim_threshold
                )
            skip += self.conf['neo4j']['limit']
            to_fusion_entities = self.neo4j_utils.get_nodes_by_label(to_fuse_label, skip, self.conf['neo4j']['limit'])
        if fusion_type == 'ontology':
            node_type = EntityAlign.TypeChoices.ONTOLOGY
        else:
            node_type = EntityAlign.TypeChoices.ENTITY
        entities = EntityAlign.objects.filter(task__id=task.id, node_type=node_type)
        for entity in entities:
            candidates = entity.candidates.values_list('score', 'name')
            if candidates:
                EntityAlign.objects.filter(id=entity.id).update(default_select=candidates[0][1])

    def _get_label(self, to_fusion_kg: str, fusion_type: str) -> str:
        if fusion_type == 'ontology':
            return f'{to_fusion_kg}:concept'
        return to_fusion_kg

    def _align_entity(
        self, to_fusion_entity: EntityTriples, base_kg: str, to_fusion_kg: str, fusion_type: str,
        task: UpdateTask, synonym_dictory_id: Optional[str], sim_threshold: Optional[float] = None
    ) -> Optional[List[Dict]]:
        candidates, synonym_candidates = self.candidate_generator.generate(
            to_fusion_entity.head.name, base_kg, fusion_type,
            synonym_dictory_id=synonym_dictory_id
        )
        aligned_entities: Optional[List[Dict]] = self.entity_alignment.align(
            to_fusion_entity, candidates, synonym_candidates, sim_threshold
        )
        self._update_mysql(to_fusion_entity, aligned_entities, base_kg, to_fusion_kg, fusion_type, task)
        return aligned_entities

    def _update_mysql(self, to_fusion_entity: EntityTriples, aligned_entities: Optional[List[Dict]], base_kg: str,
                      to_fusion_kg: str, fusion_type: str, task: UpdateTask) -> None:
        if aligned_entities:
            aligned_entity = aligned_entities[0]['candidate']
            entity_align = self.mysql_utils.add_entity_align(aligned_entity.head, base_kg, aligned_entity.score,
                                                             fusion_type, task, 'merge')
            self.mysql_utils.add_source(to_fusion_entity.head, to_fusion_kg, entity_align)
            self.mysql_utils.add_source(aligned_entity.head, base_kg, entity_align)
            for candidate in aligned_entities:
                self.mysql_utils.add_candidate([candidate['candidate']], entity_align)
        else:
            entity_align = self.mysql_utils.add_entity_align(to_fusion_entity.head, to_fusion_kg, 0, fusion_type, task,
                                                             'add')
            self.mysql_utils.add_source(to_fusion_entity.head, to_fusion_kg, entity_align)

    def triple_fusion(self, base_kg: str, to_fusion_kg: str, task_id: int, fusion_type: str) -> None:
        """三元组融合

        :param base_kg: 基础图谱
        :type base_kg: str
        :param to_fusion_kg: 待融合图谱
        :type to_fusion_kg: str
        """
        # TODO: triple_fusion的传参最好也是task+fusion_type
        self._update_entity(base_kg, to_fusion_kg, fusion_type, task_id)
        self.neo4j_utils.kg_mapper.neo4j_graph.node_create_executor.wait_until_empty()
        self.es_utils.es_client.create_executor.wait_until_empty()
        # TODO: 所有类似的都可以用先获得数量，再用for循环
        to_fuse_label = self._get_label(to_fusion_kg, fusion_type)
        skip = 0
        to_fusion_entities = self.neo4j_utils.get_nodes_by_label(to_fuse_label, skip, self.conf['neo4j']['limit'])
        to_create_edges: List[Edge] = []
        while to_fusion_entities:
            for entity in to_fusion_entities:
                if fusion_type == 'entity' and 'concept' in entity.labels:  # 只能先以这个方法取实体
                    continue
                entity = Entity(eid=entity.properties['_eid'],
                                name=entity.properties['name'],
                                type=entity.properties.get('_type', '其他'))
                logger.info(f'开始融合{entity.name}')
                aligned_entity = self._fuse_triple(entity, base_kg, to_fusion_kg, task_id, fusion_type)
                self._update_neo4j(aligned_entity, base_kg, to_create_edges)
                # self._update_es(aligned_entity, base_kg)
            skip += self.conf['neo4j']['limit']
            to_fusion_entities = self.neo4j_utils.get_nodes_by_label(to_fuse_label, skip, self.conf['neo4j']['limit'])
        self.neo4j_utils.kg_mapper.neo4j_graph.create_edges(to_create_edges, sync=True, batch_size=1000)
        node_type = FusionTriple.TypeChoices.ONTOLOGY if fusion_type == 'ontology' else FusionTriple.TypeChoices.ENTITY
        triples = FusionTriple.objects.filter(task__id=task_id, node_type=node_type)
        for triple in triples:
            FusionTriple.objects.filter(id=triple.id).update(default_select=triple
                                                             .sources.values_list('triple', flat=True)[0])
        self.neo4j_utils.kg_mapper.neo4j_graph.edge_create_executor.wait_until_empty()

    def _update_entity(self, base_kg: str, to_fusion_kg: str, fusion_type: str, task_id: int) -> None:
        to_fuse_label = self._get_label(to_fusion_kg, fusion_type)
        skip = 0
        to_fusion_entities = self.neo4j_utils.get_nodes_by_label(to_fuse_label, skip, self.conf['neo4j']['limit'])
        neo4j_nodes = []
        # TODO: 改成for循环，先差数量，再循环
        while to_fusion_entities:
            for entity in to_fusion_entities:
                if fusion_type == 'entity' and 'concept' in entity.labels:  # 只能先以这个方法取实体
                    continue
                resource_type = 'entity'
                extract_task_id_ = ''
                type_ = 1
                predicate = 'name'
                resource_name = entity.properties['name']
                resource_id = entity.properties['_eid']
                resource_type = 'entity'
                source_name = f'{resource_name}_name_{resource_name}'
                yfid_ = yfid(f"{resource_name}__{resource_name}__{predicate}__{type_}__{task_id}__{extract_task_id_}")
                resource_yfid = ResourceChangeLog.create_yfid(source_name, yfid_, 'generate', '')
                resource_log = ResourceChangeLog.objects.filter(yfid=resource_yfid, resource_name=source_name).first()
                self._bulk_log([{
                    'resource_name': resource_name,
                    'resource_id': resource_id,
                    'resource_type': resource_type,
                    'operation': 'create',
                    'value': '',
                    'source_id': resource_log.resource_id if resource_log else yfid_,
                }])
                entity = Entity(eid=entity.properties['_eid'],
                                name=entity.properties['name'],
                                type=entity.properties.get('_type', '其他'))
                aligned_entity_mysql = self.mysql_utils.get_aligned_entity(entity, task_id)
                if aligned_entity_mysql.status == EntityAlign.FusionTypeChoices.CREATE:
                    properties = {'name': entity.name, '_eid': entity.eid, '_show_name': entity.name}
                    logger.info(f"aligned_entity_mysql properties: {properties}, {to_fusion_kg=}, {base_kg=}")
                    node = self.neo4j_utils.create_entity_without_properties(entity, base_kg, fusion_type)
                    neo4j_nodes.append(node)
                    self.es_utils._create_es_entity(entity, base_kg, fusion_type)
            skip += self.conf['neo4j']['limit']
            to_fusion_entities = self.neo4j_utils.get_nodes_by_label(to_fuse_label, skip, self.conf['neo4j']['limit'])
        self.neo4j_utils.kg_mapper.neo4j_graph.create_nodes(neo4j_nodes, sync=True, batch_size=1000)

    @close_old_database_connections
    def _bulk_log(self, logs: List[Dict[str, str]]) -> None:
        for log in logs:
            self.resource_change_log_executor.add_task(**log)

    def _fuse_triple(self, entity: Entity, base_kg_label: str, to_fusion_kg_label: str, task_id: int, fusion_type):
        node_type = FusionTriple.TypeChoices.ONTOLOGY if fusion_type == 'ontology' else FusionTriple.TypeChoices.ENTITY
        to_fusion_candidate, aligned_candidate = self._get_aligned_entity(entity, to_fusion_kg_label,
                                                                          fusion_type, task_id)
        to_fusion_kg_id = to_fusion_kg_label[2:]
        base_kg_id = base_kg_label[2:]
        base_kg = Kg.objects.get(id=base_kg_id)
        to_fusion_kg = Kg.objects.get(id=to_fusion_kg_id)
        task = UpdateTask.objects.get(id=task_id)
        if not aligned_candidate:
            # 新增
            aligned_entity = self._fuse_tail(to_fusion_candidate, base_kg, to_fusion_kg, task, fusion_type)
            for attribute in aligned_entity.attributes:
                mysql_triple = f'<{aligned_entity.head.name}, {attribute.attribute_name}, {attribute.attribute_value}>'
                fusion_triple = self.mysql_utils.add_fusion_triple(mysql_triple, FusionTriple.FusionTypeChoices.CREATE,
                                                                   FusionTriple.TripleType.ATTRIBUTE, task,
                                                                   node_type)
                self.mysql_utils.add_source_triple(mysql_triple, to_fusion_kg, fusion_triple)
            logger.info(f'新增{aligned_entity=}')
            resource_type = 'entity'
            extract_task_id_ = ''
            type_ = 1
            predicate = 'name'
            resource_name = to_fusion_candidate.head.name
            resource_id = to_fusion_candidate.head.eid
            resource_type = 'entity'
            logger.info(f'新增 {resource_name=}  {resource_id=}')
            source_name = f'{resource_name}_name_{resource_name}'
            yfid_ = yfid(f"{resource_name}__{resource_name}__{predicate}__{type_}__{task_id}__{extract_task_id_}")
            resource_yfid = ResourceChangeLog.create_yfid(source_name, yfid_, 'generate', '')
            resource_log = ResourceChangeLog.objects.filter(yfid=resource_yfid, resource_name=source_name).first()
            self._bulk_log([{
                'resource_name': resource_name,
                'resource_id': resource_id,
                'resource_type': resource_type,
                'operation': 'create',
                'value': '',
                'source_id': resource_log.resource_id if resource_log else yfid_,
            }])
        else:
            aligned_entity = self._fuse_relation(aligned_candidate, to_fusion_candidate, base_kg, to_fusion_kg, task,
                                                 fusion_type)
            aligned_entity = self._fuse_attribute(aligned_candidate, to_fusion_candidate, task, base_kg,
                                                  to_fusion_kg, fusion_type)
            resource_name = to_fusion_candidate.head.name
            resource_id = to_fusion_candidate.head.eid
            resource_type = 'entity'
            logger.info(f'融合 {resource_name=}  {resource_id=}  {aligned_candidate.head.eid=}')
            self._bulk_log([{
                'resource_name': aligned_candidate.head.name,
                'resource_id': aligned_candidate.head.eid,
                'resource_type': resource_type,
                'operation': 'merge',
                'value': '',
                'source_id': [resource_id, aligned_candidate.head.eid],
                'detail': [{'key': '相似度', 'value': aligned_candidate.score}],
            }])
        return aligned_entity

    def _fuse_tail(self, entity: EntityTriples, base_kg: Kg, to_fusion_kg: Kg, task: UpdateTask,
                   fusion_type: str) -> EntityTriples:
        node_type = FusionTriple.TypeChoices.ONTOLOGY if fusion_type == 'ontology' else FusionTriple.TypeChoices.ENTITY
        for relation in entity.relations:
            aligned_entity_mysql = self.mysql_utils.get_aligned_entity(relation.tail, task.id)
            aligned_entity = self.neo4j_utils.get_node_by_eid(aligned_entity_mysql.eid,
                                                              f'KG{aligned_entity_mysql.kg.id}')
            aligned_entity = Entity(eid=aligned_entity.properties['_eid'],
                                    name=aligned_entity.properties['name'],
                                    type=aligned_entity.properties.get('_type', '其他'))
            relation.tail = aligned_entity
            relation.type = 'add'
            mysql_triple = f'<{entity.head.name}, {relation.relation}, {relation.tail.name}>'
            fusion_triple = self.mysql_utils.add_fusion_triple(mysql_triple, FusionTriple.FusionTypeChoices.CREATE,
                                                               FusionTriple.TripleType.RELATION, task, node_type)
            self.mysql_utils.add_source_triple(mysql_triple, to_fusion_kg, fusion_triple)
        return entity

    def _get_aligned_entity(self, entity: Entity, to_fusion_kg: str,
                            fusion_type: str, task_id: int) -> Tuple[EntityTriples, Optional[EntityTriples]]:
        to_fusion_entity = self.candidate_generator.generate_candidate(entity, to_fusion_kg, fusion_type)
        aligned_entity_mysql = self.mysql_utils.get_aligned_entity(to_fusion_entity.head, task_id)
        if aligned_entity_mysql.status == EntityAlign.FusionTypeChoices.CREATE:
            return to_fusion_entity, None
        logger.info(f'融合 {to_fusion_entity.head.name=}  {aligned_entity_mysql.name=}')
        logger.info(f'{aligned_entity_mysql.eid=}  KG{aligned_entity_mysql.kg.id}')
        aligned_entity = self.neo4j_utils.get_node_by_eid(aligned_entity_mysql.eid, f'KG{aligned_entity_mysql.kg.id}')
        aligned_entity = Entity(eid=aligned_entity.properties['_eid'],
                                name=aligned_entity.properties['name'],
                                type=aligned_entity.properties.get('_type', '其他'))
        aligned_entity = self.candidate_generator.generate_candidate(aligned_entity, f'KG{aligned_entity_mysql.kg.id}',
                                                                     fusion_type, score=aligned_entity_mysql.score)
        return to_fusion_entity, aligned_entity

    def _update_es(self, entity: EntityTriples, base_kg: str) -> None:
        # TODO: 确认下这个type是不是要更新
        for relation in entity.relations:
            tail = relation.tail
            if tail.type == '其他':
                continue
            logger.info(f'_update_es {tail.name=}  {tail.eid=}')
            es_tail = self.es_utils.get_entity_by_id(tail.eid)
            if es_tail['type'] != tail.type:
                body = {'doc': {'type': tail.type}}
                self.es_utils.update_entity(body, tail.eid)

    def _update_neo4j(self, aligned_entity: EntityTriples, base_kg: str, to_create_edges: List) -> None:
        self._update_attributes(aligned_entity, base_kg)
        self._update_relations(aligned_entity, base_kg, to_create_edges)

    def _update_attributes(self, entity: EntityTriples, label: str) -> Node:
        if self.neo4j_utils.get_node_by_eid(entity.head.eid, label):
            node = self.neo4j_utils.update_attributes(entity, label)
        else:
            node = self.neo4j_utils.create_entity(entity, label)
        return node

    def _update_relations(self, entity: EntityTriples, label: str, to_create_edges: List) -> None:
        head_eid = entity.head.eid
        relations = entity.relations
        for relation in relations:
            tail = relation.tail
            tail_eid = tail.eid
            path = {'head_eid': head_eid, 'tail_eid': tail_eid, 'relation_name': relation.relation}
            logger.info(f'_update_relations {path=}  {label=}')
            if relation.type == 'add':
                edge = self.neo4j_utils.create_relation(path, label)
                to_create_edges.append(edge)

    def _fuse_relation(self, entity: EntityTriples, to_fusion_entity: EntityTriples, base_kg: Kg, to_fusion_kg: Kg,
                       task: UpdateTask, fusion_type: str) -> EntityTriples:
        # TODO: 1.四个参数，想办法减少
        # TODO: 2.调用了generate_candidate这个函数，传入的第一个参数应该是个hit，在这是个entity
        # TODO: 但是用到的东西是相同的（e_id和name）, 看怎么改generate_candidate这个函数的输入输出
        # TODO: 思路：改为传e_id和name，但是会多一个参数
        node_type = FusionTriple.TypeChoices.ONTOLOGY if fusion_type == 'ontology' else FusionTriple.TypeChoices.ENTITY
        relations = copy.deepcopy(entity.relations)  # 不改变原来的relations
        to_fusion_relations = to_fusion_entity.relations
        for to_fusion_relation in to_fusion_relations:
            num = self.relation_alignment.align(to_fusion_relation, entity.relations)  # 只和之前的做对齐，新对齐的不参加
            aligned_entity_mysql = self.mysql_utils.get_aligned_entity(to_fusion_relation.tail, task.id)
            if num is not None and aligned_entity_mysql.eid == relations[num].tail.eid:  # 小心为0，
                # 融合
                mysql_triple_base = f'<{entity.head.name}, {relations[num].relation}, {relations[num].tail.name}>'
                mysql_triple_to_fuse = (f'<{to_fusion_entity.head.name}, {to_fusion_relation.relation}, '
                                        f'{to_fusion_relation.tail.name}>')
                mysql_triple_fused = mysql_triple_base  # TODO: 就是为了看的更清楚
                fusion_triple = self.mysql_utils.add_fusion_triple(mysql_triple_fused,
                                                                   FusionTriple.FusionTypeChoices.MERGE,
                                                                   FusionTriple.TripleType.RELATION, task, node_type)
                self.mysql_utils.add_source_triple(mysql_triple_base, base_kg, fusion_triple)
                self.mysql_utils.add_source_triple(mysql_triple_to_fuse, to_fusion_kg, fusion_triple)
                continue
            # 新增，要不然是关系，要不然是实体
            if num is None:
                to_fusion_relation.type = 'add'
                relations.append(to_fusion_relation)
            else:
                relations.append(
                    Relation(relation=entity.relations[num].relation, tail=to_fusion_relation.tail, type='add'))
            if aligned_entity_mysql.kg.id == base_kg.id:
                aligned_entity = self.neo4j_utils.get_node_by_eid(aligned_entity_mysql.eid,
                                                                  f'KG{aligned_entity_mysql.kg.id}')
                relations[-1].tail = Entity(eid=aligned_entity.properties['_eid'],
                                            name=aligned_entity.properties['name'],
                                            type=aligned_entity.properties.get('_type', '其他'))
            mysql_triple = f'<{entity.head.name}, {relations[-1].relation}, {relations[-1].tail.name}>'
            fusion_triple = self.mysql_utils.add_fusion_triple(mysql_triple, FusionTriple.FusionTypeChoices.CREATE,
                                                               FusionTriple.TripleType.RELATION, task, node_type)
            self.mysql_utils.add_source_triple(mysql_triple, to_fusion_kg, fusion_triple)
        entity.relations = relations
        return entity

    def _fuse_attribute(self, entity: EntityTriples, to_fusion_entity: EntityTriples, task: UpdateTask,
                        base_kg: Kg, to_fusion_kg: Kg, fusion_type: str) -> EntityTriples:
        node_type = FusionTriple.TypeChoices.ONTOLOGY if fusion_type == 'ontology' else FusionTriple.TypeChoices.ENTITY
        attributes = entity.attributes
        to_fusion_attributes = to_fusion_entity.attributes
        for to_fusion_attribute in to_fusion_attributes:
            num = self.attribute_name_alignment.align(to_fusion_attribute, attributes)
            if num is None:
                # 新增
                attributes.append(to_fusion_attribute)
                mysql_triple = (f'<{entity.head.name}, {to_fusion_attribute.attribute_name}, '
                                f'{to_fusion_attribute.attribute_value}>')
                source_triple = (f'<{to_fusion_entity.head.name}, {to_fusion_attribute.attribute_name}, '
                                 f'{to_fusion_attribute.attribute_value}>')
                fusion_triple = self.mysql_utils.add_fusion_triple(mysql_triple, FusionTriple.FusionTypeChoices.CREATE,
                                                                   FusionTriple.TripleType.ATTRIBUTE, task,
                                                                   node_type)
                self.mysql_utils.add_source_triple(source_triple, to_fusion_kg, fusion_triple)
            else:
                # 融合
                mysql_triple_base = (f'<{entity.head.name}, {attributes[num].attribute_name}, '
                                     f'{attributes[num].attribute_value}>')
                attributes[num] = self.attribute_value_alignment.align(attributes[num], to_fusion_attribute)
                mysql_triple_fused = (f'<{entity.head.name}, {attributes[num].attribute_name}, '
                                      f'{attributes[num].attribute_value}>')
                mysql_triple_to_fuse = (f'<{to_fusion_entity.head.name}, {to_fusion_attribute.attribute_name}, '
                                        f'{to_fusion_attribute.attribute_value}>')
                fusion_triple = self.mysql_utils.add_fusion_triple(mysql_triple_fused,
                                                                   FusionTriple.FusionTypeChoices.MERGE,
                                                                   FusionTriple.TripleType.ATTRIBUTE, task,
                                                                   node_type)
                self.mysql_utils.add_source_triple(mysql_triple_base, base_kg, fusion_triple)
                self.mysql_utils.add_source_triple(mysql_triple_to_fuse, to_fusion_kg, fusion_triple)
        entity.attributes = attributes
        return entity
