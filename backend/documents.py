from elasticsearch_dsl import Date, DenseVector, Document, Integer, Keyword, Text
from yunfu.common import ConfigUtils
from yunfu.yfint.schema import Entity


class Linker(Entity):
    """
    链接表
    :field name: 实体名/别名
    :field linked: 实体名
    :field status: 实体状态
    :field _type: 类型
    :field created_at: 创建时间
    """
    show_name = Text(analyzer='ik_max_word', search_analyzer='ik_searcher')
    linked_name = Text(analyzer='ik_max_word', search_analyzer='ik_searcher')
    embedding = DenseVector(dims=768)
    content_type = Keyword()
    version = Keyword()
    created_at = Date()
    node_type = Keyword()
    kg_id = Integer()

    class Index:
        settings = ConfigUtils.load('conf/config.yaml')['es_analysis']


class SynonymsDictory(Document):
    synonym_dictory_id = Keyword()
    synonyms = Keyword(multi=True)
    created_at = Date()
    updated_at = Date()

    class Index:
        name = 'synonyms_dictories'
