{
  "editor.tabSize": 2,
  "editor.formatOnSave": true,
  "editor.suggestSelection": "first",
  "editor.columnSelection": false,
  "editor.bracketPairColorization.enabled": true,
  "editor.guides.bracketPairs": "active",
  "editor.unicodeHighlight.ambiguousCharacters": false,
  "editor.codeActionsOnSave": {
    "source.organizeImports": "explicit"
  },
  "editor.minimap.enabled": false,
  "editor.wordWrap": "on",
  "explorer.confirmDragAndDrop": false,
  "explorer.confirmDelete": false,
  "search.followSymlinks": false,
  "workbench.editor.enablePreview": true,
  // 忽略展示的文件
  "files.exclude": {
    "**/.mypy_cache": true,
    "**/.pytest_cache": true,
    "**/__pycache__": true,
    "**/node_modules": true
  },
  // python
  "python.testing.pytestEnabled": false,
  "flake8.args": [
    "--max-line-length=120",
    "--config=${workspaceFolder}/.flake8"
  ],
  "flake8.importStrategy": "fromEnvironment",
  "flake8.ignorePatterns": ["frontend"],
  "mypy-type-checker.args": ["--config-file", "${workspaceFolder}/mypy.ini"],
  "mypy-type-checker.ignorePatterns": ["**/tests/**/*.py"],
  "isort.args": ["--atomic", "-l", "120"],
  "autoDocstring.docstringFormat": "sphinx",
  "files.associations": {
    "**/requirements{/**,*}.{txt,in}": "pip-requirements",
    "**/*.jsonnet": "json",
    "*.vue": "vue",
    "*.css": "tailwindcss"
  },
  // git
  "git.confirmSync": false,
  "git.enableSmartCommit": true,
  "workbench.colorTheme": "Dracula Theme",
  "github.copilot.editor.enableCodeActions": false,
  "github.copilot.enable": {
    "*": false
  },
  "trailing-spaces.trimOnSave": true,
  "[python]": {
    "editor.defaultFormatter": "ms-python.black-formatter",
    "editor.tabSize": 4,
    "editor.formatOnSave": true,
    "editor.formatOnSaveMode": "file"
  },
  "editor.fontFamily": "Maple Mono NF CN",
  "editor.fontLigatures": true
}
