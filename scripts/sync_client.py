from yfflow import Client

client = Client('*************', 18032, 'GRPC')

# test = {
#     'feature_set': {
#         'nes': [{
#             'text': '张三的大力丸得了李四感冒'
#         }]
#     },
#     'index': 'linking_kg161',
#     'check_type': 0,
#     'top': 5,
#     'recall_limit': 5,
#     'threshold': 0.1,
#     'kg_id': 123
# }
test = {'task_id': 2199}
# res = client.post('/kgs/knowledge_fusion/', test)
res = client.post('/kgs/triple_fusion/', test)
print('---' * 20)
print(res)
# res = client.post('/api/attribute_alignment/', test)
# print('---' * 20)
# print(res)
# res = client.post('/api/relation_alignment/', test)
# print('---' * 20)
# print(res)
# res = client.post('/api/ontology_link/', test)
# print('---' * 20)
# print(res)
