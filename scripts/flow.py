import argparse

from jina import Flow

from backend.executors import NewFusionExecutor

parser = argparse.ArgumentParser(description='Options in Server')
parser.add_argument('-p', '--port', default=8000, type=int, help='server port')
# 如果服务为远程executor中的一环，应修改并使用GRPC协议
parser.add_argument('-P', '--protocol', default='GRPC', help='server protocol GRPC or HTTP')
parser.add_argument('-m', '--port-monitoring', default=8080, type=int, help='port of prometheus metrics')
args = parser.parse_args()

_flow = (
    Flow(
        protocol=args.protocol,
        port_expose=args.port,
        monitoring=True,
        port_monitoring=args.port_monitoring,
    )
    .add(uses=NewFusionExecutor, name='PropertyExecutor', replicas=6)
)

with _flow:
    _flow.block()
