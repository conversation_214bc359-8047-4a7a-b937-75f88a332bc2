import time

import pytest
from yunfu.common import yfid
from yunfu.db.graph.models import Edge, Node, Space

from backend.fusion.schema import Attribute, Entity, EntityTriples


@pytest.fixture(scope="session")
def test_space_name():
    return "KGFUSION"


@pytest.fixture(scope="session")
def test_space(test_space_name):
    return Space(
        name=test_space_name,
        props=[
            {"name": "name", "type": "string"},
            {"name": "test_show_name", "type": "string"},
            {"name": "type", "type": "string"},
            {"name": "eid", "type": "string"},
            {"name": "create_time", "type": "string"},
            {"name": "update_time", "type": "string"},
        ],
    )


@pytest.fixture(scope="session")
def test_entity_1():
    """测试实体1"""
    return Entity(
        eid="test_eid_001",
        name="测试实体1",
        type="测试类型"
    )


@pytest.fixture(scope="session")
def test_entity_2():
    """测试实体2"""
    return Entity(
        eid="test_eid_002",
        name="测试实体2",
        type="测试类型"
    )


@pytest.fixture(scope="session")
def test_entity_triples_1(test_entity_1):
    """测试实体三元组1"""
    return EntityTriples(
        head=test_entity_1,
        relations=[],
        attributes=[
            Attribute(attribute_name="属性1", attribute_value="值1"),
            Attribute(attribute_name="属性2", attribute_value="值2"),
        ]
    )


@pytest.fixture(scope="session")
def test_entity_triples_2(test_entity_2):
    """测试实体三元组2"""
    return EntityTriples(
        head=test_entity_2,
        relations=[],
        attributes=[
            Attribute(attribute_name="属性3", attribute_value="值3"),
            Attribute(attribute_name="属性4", attribute_value="值4"),
        ]
    )


@pytest.fixture(scope="session")
def test_nodes(test_entity_1, test_entity_2):
    """测试节点数据"""
    return [
        Node(
            id=test_entity_1.eid,
            types=["KGFUSION"],
            props={
                "name": test_entity_1.name,
                "_eid": test_entity_1.eid,
                "_show_name": test_entity_1.name,
                "type": test_entity_1.type,
                "属性1": "值1",
                "属性2": "值2"
            }
        ),
        Node(
            id=test_entity_2.eid,
            types=["KGFUSION"],
            props={
                "name": test_entity_2.name,
                "_eid": test_entity_2.eid,
                "_show_name": test_entity_2.name,
                "type": test_entity_2.type,
                "属性3": "值3",
                "属性4": "值4"
            }
        )
    ]


@pytest.fixture(scope="session")
def test_edge(test_entity_1, test_entity_2):
    """测试边数据"""
    rid = yfid(f'测试关系{time.time()}')[:6]
    return Edge(
        id=rid,
        src_id=test_entity_1.eid,
        dst_id=test_entity_2.eid,
        type="关联",
        props={
            "name": "测试关系",
            "c": True,
            "_rid": rid
        }
    )
