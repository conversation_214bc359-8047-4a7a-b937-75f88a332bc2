from typing import List
from unittest.mock import MagicMock, patch

import pytest
from yunfu.db.graph.models import Edge, Node, Space

from backend.fusion.schema import Attribute, EntityTriples
from backend.graph import NebulaGraphMapper, Neo4jGraphMapper


class TestGraphMapper:
    """测试查询类方法，比较 Neo4j 和 Nebula 结果是否一致"""

    def test_get_node_by_eid_consistency(
        self,
        neo4j_mapper: Neo4jGraphMapper,
        nebula_mapper: NebulaGraphMapper,
        neo4j_space: str,
        nebula_space: str,
        neo4j_eid: str,
        nebula_eid: str
    ):
        """测试根据 eid 获取节点的一致性"""
        # 获取 Neo4j 结果
        neo4j_node = neo4j_mapper.get_node_by_eid(neo4j_space, neo4j_eid)
        # 获取 Nebula 结果
        nebula_node = nebula_mapper.get_node_by_eid(nebula_space, nebula_eid)

        # 比较结果结构一致性
        assert neo4j_node.props["name"] == nebula_node.props["name"]

    def test_get_nodes_by_label_consistency(
        self,
        neo4j_mapper: Neo4jGraphMapper,
        nebula_mapper: NebulaGraphMapper,
        neo4j_space: str,
        nebula_space: str
    ):
        """测试根据标签获取节点列表的一致性"""
        skip, limit = 0, 1000
        # 获取 Neo4j 结果
        neo4j_nodes = neo4j_mapper.get_nodes_by_label(neo4j_space, skip, limit)
        # 获取 Nebula 结果
        nebula_nodes = nebula_mapper.get_nodes_by_label(nebula_space, skip, limit)
        # 检查返回的节点数量不超过限制
        assert len(neo4j_nodes) == len(nebula_nodes)

    def test_get_edges_by_head_eid_consistency(
        self,
        neo4j_mapper: Neo4jGraphMapper,
        nebula_mapper: NebulaGraphMapper,
        neo4j_space: str,
        nebula_space: str,
        neo4j_eid: str,
        nebula_eid: str
    ):
        """测试根据头节点 eid 获取边的一致性"""
        # 获取 Neo4j 结果
        neo4j_edges = neo4j_mapper.get_edges_by_head_eid(neo4j_space, neo4j_eid)
        # 获取 Nebula 结果
        nebula_edges = nebula_mapper.get_edges_by_head_eid(nebula_space, nebula_eid)
        # 检查边数量一致
        assert len(neo4j_edges) == len(nebula_edges)


class TestGraphMapperWrite:

    def set_up_space(self, nebula_mapper, test_space):
        space = Space(
            name=test_space,
            props=[
                {"name": "name", "type": "string"},
                {"name": "test_show_name", "type": "string"},
                {"name": "type", "type": "string"},
                {"name": "eid", "type": "string"},
                {"name": "create_time", "type": "string"},
                {"name": "update_time", "type": "string"},
            ],
        )
        if nebula_mapper.graph_db.has_space(test_space):
            nebula_mapper.graph_db.drop_space(test_space)
        nebula_mapper.graph_db.create_space(space)
