import pytest

from backend.graph import NebulaGraphMapper, Neo4jGraphMapper


@pytest.fixture(scope="session")
def nebula_kg_id():
    return 125276


@pytest.fixture(scope="session")
def nebula_space(nebula_kg_id):
    return f"KG{nebula_kg_id}"


@pytest.fixture(scope="session")
def nebula_eid():
    return "ETDK3BwDxoL9VZUkcBCh4Q"


@pytest.fixture(scope="session")
def neo4j_kg_id():
    return 125279


@pytest.fixture(scope="session")
def neo4j_space(neo4j_kg_id):
    return f"KG{neo4j_kg_id}"


@pytest.fixture(scope="session")
def neo4j_eid():
    return "Itk3tC0UMVaob_8NU1Guvw"


@pytest.fixture(scope="session")
def nebula_graph_config():
    return {
        "host": "*************",
        "port": 9669,
        "username": "root",
        "password": "root",
    }


@pytest.fixture(scope="session")
def neo4j_graph_config():
    return {
        "host": "*************",
        "port": 20873,
        "username": "neo4j",
        "password": "yunfu2017",
    }


@pytest.fixture(scope="session")
def nebula_mapper(nebula_graph_config):
    return NebulaGraphMapper(nebula_graph_config, enable_version=True)


@pytest.fixture(scope="session")
def neo4j_mapper(neo4j_graph_config):
    return Neo4jGraphMapper(neo4j_graph_config, enable_version=True)


@pytest.fixture(scope="session")
def test_version():
    return "c"
