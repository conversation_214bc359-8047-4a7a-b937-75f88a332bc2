import pytest

from backend.fusion.schema.entity import Entity
from backend.fusion.schema.entity_triple import EntityTriples


@pytest.mark.usefixtures('entity_alignment')
class TestEntityAlignment:
    def test_align(self, entity_alignment):
        entity = EntityTriples(head=Entity(eid='7SH3bu766w5A1evlBCib7Q', name='在踝和足水平血管的损伤', type='其他'), relations=[], attributes=[])
        candidates = [
            EntityTriples(head=Entity(eid='_E3p41-wsKDtHc4qI9ZYsQ', name='在踝和足水平的多血管损伤', type='其他'), relations=[], attributes=[]),
            EntityTriples(head=Entity(eid='8gLo362GXKAGLGsjCXiwNg', name='在踝和足水平的神经损伤', type='其他'), relations=[], attributes=[]),
            EntityTriples(head=Entity(eid='7YiiUYE9lkEg_1e3vCEzug', name='踝和足多处血管损伤', type='其他'), relations=[], attributes=[]),
            EntityTriples(head=Entity(eid='358DVkPZYF-TQSu0HMCIsQ', name='踝和足水平血管损伤', type='其他'), relations=[], attributes=[]),
            EntityTriples(head=Entity(eid='MBW-du4zFbX-hyCc19GOiw', name='在踝和足水平的血管损伤', type='其他'), relations=[], attributes=[]),
        ]
        aligned_entity = entity_alignment.align(entity, candidates)[0]['candidate']
        assert aligned_entity.head.eid == 'MBW-du4zFbX-hyCc19GOiw'
        assert aligned_entity.head.name == '在踝和足水平的血管损伤'

    def test_align_entity_by_similarity(self, entity_alignment):
        entity = EntityTriples(head=Entity(eid='7SH3bu766w5A1evlBCib7Q', name='在踝和足水平血管的损伤', type='其他'), relations=[], attributes=[])
        candidates = [
            EntityTriples(head=Entity(eid='_E3p41-wsKDtHc4qI9ZYsQ', name='在踝和足水平的多血管损伤', type='其他'), relations=[], attributes=[]),
            EntityTriples(head=Entity(eid='8gLo362GXKAGLGsjCXiwNg', name='在踝和足水平的神经损伤', type='其他'), relations=[], attributes=[]),
            EntityTriples(head=Entity(eid='7YiiUYE9lkEg_1e3vCEzug', name='踝和足多处血管损伤', type='其他'), relations=[], attributes=[]),
            EntityTriples(head=Entity(eid='358DVkPZYF-TQSu0HMCIsQ', name='踝和足水平血管损伤', type='其他'), relations=[], attributes=[]),
            EntityTriples(head=Entity(eid='MBW-du4zFbX-hyCc19GOiw', name='在踝和足水平的血管损伤', type='其他'), relations=[], attributes=[]),
        ]
        aligned_entities = entity_alignment.align(entity, candidates)
        assert aligned_entities[0]['candidate'].head.eid == 'MBW-du4zFbX-hyCc19GOiw'
        assert aligned_entities[0]['candidate'].head.name == '在踝和足水平的血管损伤'

        # 测试None
        entity_alignment.fast_sim_threshold = 1
        entity = EntityTriples(head=Entity(eid='7SH3bu766w5A1evlBCib7Q', name='在踝和足水平血管的损伤', type='其他'), relations=[], attributes=[])
        candidates = [
            EntityTriples(head=Entity(eid='_E3p41-wsKDtHc4qI9ZYsQ', name='在踝和足水平的多血管损伤', type='其他'), relations=[], attributes=[]),
            EntityTriples(head=Entity(eid='8gLo362GXKAGLGsjCXiwNg', name='在踝和足水平的神经损伤', type='其他'), relations=[], attributes=[]),
            EntityTriples(head=Entity(eid='7YiiUYE9lkEg_1e3vCEzug', name='踝和足多处血管损伤', type='其他'), relations=[], attributes=[]),
            EntityTriples(head=Entity(eid='358DVkPZYF-TQSu0HMCIsQ', name='踝和足水平血管损伤', type='其他'), relations=[], attributes=[]),
            EntityTriples(head=Entity(eid='MBW-du4zFbX-hyCc19GOiw', name='在踝和足水平的血管损伤', type='其他'), relations=[], attributes=[]),
        ]
        aligned_entity = entity_alignment.align(entity, candidates)
        assert aligned_entity is None
        entity_alignment.fast_sim_threshold = 0.5
