import pytest
from yunfu.common import ConfigUtils

from backend.fusion.entity_alignment import EntityAlignment
from backend.fusion.relation_alignment import RelationAlignment


@pytest.fixture(scope='session')
def relation_alignment():
    return RelationAlignment()


@pytest.fixture(scope='session')
def entity_alignment():
    conf_file = 'conf/config.yaml'
    return EntityAlignment(ConfigUtils.load(conf_file))
