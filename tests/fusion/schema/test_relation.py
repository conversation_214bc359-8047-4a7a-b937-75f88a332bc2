from backend.fusion.schema.entity import Entity
from backend.fusion.schema.relation import Relation


class TestRelation:
    def test_relation(self):
        relation = Relation(relation='relation', tail=Entity(eid='eid', name='name', type='其他'), type='base')
        assert relation.relation == 'relation'
        assert relation.tail.eid == 'eid'
        assert relation.tail.name == 'name'
        assert relation.tail.type == '其他'
