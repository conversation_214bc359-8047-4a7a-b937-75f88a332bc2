import pytest

from backend.fusion.schema.entity_triple import EntityTriples


@pytest.mark.usefixtures('relation_alignment')
class TestRelationAlignment:

    def test_align(self, relation_alignment):
        to_fusion_entity = EntityTriples(
            **{
                'attributes': [{
                    'attribute_name': '_type',
                    'attribute_value': '手术'
                }, {
                    'attribute_name': 'name',
                    'attribute_value': '大脑半球切除术'
                }, {
                    'attribute_name': '手术操作编码',
                    'attribute_value': '01.5200'
                }],
                'head': {
                    'eid': '1vG2pFuVZADuiYpwE7dAKw',
                    'name': '大脑半球切除术',
                    'type': '其他'
                },
                'relations': [{
                    'relation': '属于',
                    'tail': {
                        'eid': 'M2dG4-P7k95ez85LDp_z5Q',
                        'name': '手术',
                        'type': '其他'
                    },
                    'type': 'base'
                }]
            })
        entity = EntityTriples(
            **{
                'attributes': [{
                    'attribute_name': '_type',
                    'attribute_value': '手术操作（肿瘤科）'
                }, {
                    'attribute_name': 'name',
                    'attribute_value': '大脑半球切除术'
                }, {
                    'attribute_name': 'Semantic Tag',
                    'attribute_value': 'Operation'
                }],
                'head': {
                    'eid': 'QoljghC9f7x7_rUomtgGBQ',
                    'name': '大脑半球切除术',
                    'type': '其他'
                },
                'relations': [{
                    'relation': '手术相关科室',
                    'tail': {
                        'eid': 'qgR6v9NeMjFWACYu4Um5_A',
                        'name': '肿瘤科',
                        'type': '其他'
                    },
                    'type': 'base'
                }, {
                    'relation': '是...的手术',
                    'tail': {
                        'eid': 'Je0bVk3GCvZW699BJ6sRXQ',
                        'name': '癫痫',
                        'type': '其他'
                    },
                    'type': 'base'
                }, {
                    'relation': '手术相关科室',
                    'tail': {
                        'eid': 'Iq6VIReS-0xiSFzKDwjOzg',
                        'name': '神经外科',
                        'type': '其他'
                    },
                    'type': 'base'
                }, {
                    'relation': '属于',
                    'tail': {
                        'eid': 'HoFfoOnfErs55lRrxiIP4Q',
                        'name': '手术操作（神经外科）',
                        'type': '其他'
                    },
                    'type': 'base'
                }, {
                    'relation': '属于',
                    'tail': {
                        'eid': 'scCsUtX28iRFD393dSRRHw',
                        'name': '手术操作（肿瘤科）',
                        'type': '其他'
                    },
                    'type': 'base'
                }]
            })
        num = relation_alignment.align(to_fusion_entity.relations[0], entity.relations)
        assert num == 4

        to_fusion_entity = EntityTriples(
            **{
                'attributes': [{
                    'attribute_name': '_type',
                    'attribute_value': '手术'
                }, {
                    'attribute_name': 'name',
                    'attribute_value': '大脑半球切除术'
                }, {
                    'attribute_name': '手术操作编码',
                    'attribute_value': '01.5200'
                }],
                'head': {
                    'eid': '1vG2pFuVZADuiYpwE7dAKw',
                    'name': '大脑半球切除术',
                    'type': '其他'
                },
                'relations': [{
                    'relation': '属',
                    'tail': {
                        'eid': 'M2dG4-P7k95ez85LDp_z5Q',
                        'name': '手术',
                        'type': '其他'
                    },
                    'type': 'base'
                }]
            })
        entity = EntityTriples(
            **{
                'attributes': [{
                    'attribute_name': '_type',
                    'attribute_value': '手术操作（肿瘤科）'
                }, {
                    'attribute_name': 'name',
                    'attribute_value': '大脑半球切除术'
                }, {
                    'attribute_name': 'Semantic Tag',
                    'attribute_value': 'Operation'
                }],
                'head': {
                    'eid': 'QoljghC9f7x7_rUomtgGBQ',
                    'name': '大脑半球切除术',
                    'type': '其他'
                },
                'relations': [{
                    'relation': '手术相关科室',
                    'tail': {
                        'eid': 'qgR6v9NeMjFWACYu4Um5_A',
                        'name': '肿瘤科',
                        'type': '其他'
                    },
                    'type': 'base'
                }, {
                    'relation': '是...的手术',
                    'tail': {
                        'eid': 'Je0bVk3GCvZW699BJ6sRXQ',
                        'name': '癫痫',
                        'type': '其他'
                    },
                    'type': 'base'
                }, {
                    'relation': '手术相关科室',
                    'tail': {
                        'eid': 'Iq6VIReS-0xiSFzKDwjOzg',
                        'name': '神经外科',
                        'type': '其他'
                    },
                    'type': 'base'
                }, {
                    'relation': '属于',
                    'tail': {
                        'eid': 'HoFfoOnfErs55lRrxiIP4Q',
                        'name': '手术操作（神经外科）',
                        'type': '其他'
                    },
                    'type': 'base'
                }, {
                    'relation': '属于',
                    'tail': {
                        'eid': 'scCsUtX28iRFD393dSRRHw',
                        'name': '手术操作（肿瘤科）',
                        'type': '其他'
                    },
                    'type': 'base'
                }]
            })
        num = relation_alignment.align(to_fusion_entity.relations[0], entity.relations)
        assert num is None
