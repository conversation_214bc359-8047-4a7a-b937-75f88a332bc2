from abc import ABC, abstractmethod
from typing import Any, Iterable, List, Optional, Tuple

from yunfu.db.graph.models import Edge, Node

PROPS = List[Tuple[str, str, Any]]  # 属性，例如: ('name', '=', 'Tom')


class BaseNodeMatch(ABC):

    def __init__(
        self,
        graph,
        types: Iterable[str] = frozenset(),
        props: Optional[PROPS] = None,
        order_by: Optional[Iterable[str]] = None,
        skip: Optional[int] = None,
        limit: Optional[int] = None,
        where_query: Optional[str] = None,
    ) -> None:
        self.graph = graph
        self._types = frozenset(types)
        self._props = props
        self._order_by = order_by
        self._skip = skip
        self._limit = limit
        self._where_query = where_query

    @abstractmethod
    def __len__(self):
        raise NotImplementedError

    @abstractmethod
    def __iter__(self):
        raise NotImplementedError

    def all(self) -> List[Node]:
        """获取所有点"""
        return list(iter(self))

    def count(self) -> int:
        """获取点的数量"""
        return len(self)

    def rows(self, output: Optional[str] = None) -> List:
        """获取点的行"""
        return self.graph.data_wrapper.as_rows(
            self.graph.client.run(self._query(output=output)).data
        )

    def exists(self) -> bool:
        """判断是否存在点"""
        return len(self) > 0

    def first(self) -> Optional[Node]:
        """获取第一个点"""
        return next(iter(self), None)

    def limit(self, limit: int) -> "BaseNodeMatch":
        """限制点的数量"""
        return self.__class__(
            self.graph,
            self._types,
            self._props,
            self._order_by,
            self._skip,
            limit,
            self._where_query,
        )

    def order_by(self, order_by: Iterable[str]) -> "BaseNodeMatch":
        """根据属性排序"""
        return self.__class__(
            self.graph,
            self._types,
            self._props,
            order_by,
            self._skip,
            self._limit,
            self._where_query,
        )

    def skip(self, skip: int) -> "BaseNodeMatch":
        """跳过指定数量的点"""
        return self.__class__(
            self.graph,
            self._types,
            self._props,
            self._order_by,
            skip,
            self._limit,
            self._where_query,
        )

    def where(
        self,
        types: Iterable[str] = frozenset(),
        props: Optional[PROPS] = None,
        where_query: Optional[str] = None,
    ) -> "BaseNodeMatch":
        """根据属性进行过滤"""
        types = frozenset(types)
        if types and self._types:
            self._types = types.union(self._types)
        if props and self._props:
            self._props.extend(props)
        return self.__class__(
            self.graph,
            self._types,
            self._props,
            self._order_by,
            self._skip,
            self._limit,
            where_query,
        )

    def _query(self, count: bool = False, output: Optional[str] = None) -> str:
        return self.graph.template.match_node(
            self.graph.space.name,
            self.graph.version.name,
            self._types,
            self._props,
            self._order_by,
            self._skip,
            self._limit,
            count,
            self._where_query,
            output,
        )


class BaseEdgeMatch(ABC):

    def __init__(
        self,
        graph,
        types: Iterable[str] = frozenset(),
        src_node_types: Iterable[str] = frozenset(),
        dst_node_types: Iterable[str] = frozenset(),
        props: Optional[PROPS] = None,
        src_node_props: Optional[PROPS] = None,
        dst_node_props: Optional[PROPS] = None,
        order_by: Optional[Iterable[str]] = None,
        skip: Optional[int] = None,
        limit: Optional[int] = None,
        where_query: Optional[str] = None,
    ) -> None:
        self.graph = graph
        self._types = frozenset(types)
        self._src_node_types = frozenset(src_node_types)
        self._dst_node_types = frozenset(dst_node_types)
        self._props = props
        self._src_node_props = src_node_props
        self._dst_node_props = dst_node_props
        self._order_by = order_by
        self._skip = skip
        self._limit = limit
        self._where_query = where_query

    @abstractmethod
    def __len__(self):
        raise NotImplementedError

    @abstractmethod
    def __iter__(self):
        raise NotImplementedError

    def all(self) -> List[Edge]:
        """获取所有边"""
        return list(iter(self))

    def count(self) -> int:
        """获取边的数量"""
        return len(self)

    def rows(self, output: Optional[str] = None) -> List:
        """获取点的行"""
        return self.graph.data_wrapper.as_rows(
            self.graph.client.run(self._query(output=output)).data
        )

    def exists(self) -> bool:
        """判断是否存在边"""
        return len(self) > 0

    def first(self) -> Optional[Edge]:
        """获取第一条边"""
        return next(iter(self), None)

    def limit(self, limit: int) -> "BaseEdgeMatch":
        """限制边的数量"""
        return self.__class__(
            self.graph,
            self._types,
            self._src_node_types,
            self._dst_node_types,
            self._props,
            self._src_node_props,
            self._dst_node_props,
            self._order_by,
            self._skip,
            limit,
            self._where_query,
        )

    def order_by(self, order_by: Iterable[str]) -> "BaseEdgeMatch":
        """根据属性排序"""
        return self.__class__(
            self.graph,
            self._types,
            self._src_node_types,
            self._dst_node_types,
            self._props,
            self._src_node_props,
            self._dst_node_props,
            order_by,
            self._skip,
            self._limit,
            self._where_query,
        )

    def skip(self, skip: int) -> "BaseEdgeMatch":
        """跳过指定数量的边"""
        return self.__class__(
            self.graph,
            self._types,
            self._src_node_types,
            self._dst_node_types,
            self._props,
            self._src_node_props,
            self._dst_node_props,
            self._order_by,
            skip,
            self._limit,
            self._where_query,
        )

    def where(
        self,
        types: Iterable[str] = frozenset(),
        src_node_types: Iterable[str] = frozenset(),
        dst_node_types: Iterable[str] = frozenset(),
        props: Optional[PROPS] = None,
        src_node_props: Optional[PROPS] = None,
        dst_node_props: Optional[PROPS] = None,
        where_query: Optional[str] = None,
    ) -> "BaseEdgeMatch":  # type: ignore
        """根据属性和筛选条件进行过滤"""
        types = frozenset(types)
        if types and self._types:
            self._types = types.union(self._types)
        src_node_types = frozenset(src_node_types)
        if src_node_types:
            if self._src_node_types:
                self._src_node_types = src_node_types.union(self._src_node_types)
            else:
                self._src_node_types = src_node_types
        dst_node_types = frozenset(dst_node_types)
        if dst_node_types:
            if self._dst_node_types:
                self._dst_node_types = dst_node_types.union(self._dst_node_types)
            else:
                self._dst_node_types = dst_node_types
        if props and self._props:
            self._props.extend(props)
        if src_node_props:
            if self._src_node_props:
                self._src_node_props.extend(src_node_props)
            else:
                self._src_node_props = src_node_props
        if dst_node_props:
            if self._dst_node_props:
                self._dst_node_props.extend(dst_node_props)
            else:
                self._dst_node_props = dst_node_props
        return self.__class__(
            self.graph,
            self._types,
            self._src_node_types,
            self._dst_node_types,
            self._props,
            self._src_node_props,
            self._dst_node_props,
            self._order_by,
            self._skip,
            self._limit,
            where_query,
        )

    def _query(self, count: bool = False, output: Optional[str] = None) -> str:
        return self.graph.template.match_edge(
            self.graph.space.name,
            self.graph.version.name,
            self._types,
            self._src_node_types,
            self._dst_node_types,
            self._props,
            self._src_node_props,
            self._dst_node_props,
            self._order_by,
            self._skip,
            self._limit,
            count,
            self._where_query,
            output,
        )
