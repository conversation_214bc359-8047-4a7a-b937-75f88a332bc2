from .base_match import BaseEdgeMatch, BaseNodeMatch


class NebulaNodeMatch(BaseNodeMatch):

    def __len__(self):
        result = self.graph.data_wrapper.as_dict(
            self.graph.client.run(self._query(count=True)).data
        )
        return result["count(v)"]

    def __iter__(self):
        for node in self.graph.data_wrapper.as_nodes(
            self.graph.client.run(self._query()).data
        ):
            yield node


class NebulaEdgeMatch(BaseEdgeMatch):

    def __len__(self):
        result = self.graph.data_wrapper.as_dict(
            self.graph.client.run(self._query(count=True)).data
        )
        return result["count(e)"]

    def __iter__(self):
        for edge in self.graph.data_wrapper.as_edges(
            self.graph.client.run(self._query()).data
        ):
            yield edge
