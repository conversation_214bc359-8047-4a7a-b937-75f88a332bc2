from abc import ABC
from typing import Any, Iterable, List, Optional, Type

from yunfu.db.graph.models import Edge, Node

from .base_match import PROPS, BaseEdgeMatch, BaseNodeMatch


class BaseNodeMatcher(ABC):

    _match_class: Type[BaseNodeMatch]

    def __init__(self, graph) -> None:
        self.graph = graph

    def get(self, id: str) -> Optional[Node]:
        """获取节点"""
        return self.graph.get_node(id)

    def match(
        self,
        types: Iterable[str] = frozenset(),
        props: Optional[PROPS] = None,
    ) -> BaseNodeMatch:
        """查询节点"""
        types = frozenset(types)
        return self._match_class(self.graph, types, props)

    def all(
        self,
        types: Iterable[str] = frozenset(),
        props: Optional[PROPS] = None,
    ) -> List[Node]:
        """获取全部节点"""
        types = frozenset(types)
        return self._match_class(self.graph, types, props).all()

    def count(
        self,
        types: Iterable[str] = frozenset(),
        props: Optional[PROPS] = None,
    ) -> int:
        """统计节点数量"""
        types = frozenset(types)
        return self._match_class(self.graph, types, props).count()


class BaseEdgeMatcher(ABC):
    _match_class: Type[BaseEdgeMatch]

    def __init__(self, graph) -> None:
        self.graph = graph

    def get(self, src_id: str, dst_id: str, type: str, **kwargs: Any) -> Optional[Edge]:
        """获取边"""
        return self.graph.get_edge(src_id, dst_id, type, **kwargs)

    def match(
        self,
        types: Iterable[str] = frozenset(),
        src_node_types: Iterable[str] = frozenset(),
        dst_node_types: Iterable[str] = frozenset(),
        props: Optional[PROPS] = None,
        src_node_props: Optional[PROPS] = None,
        dst_node_props: Optional[PROPS] = None,
    ) -> BaseEdgeMatch:
        """查询边"""
        types = frozenset(types)
        src_node_types = frozenset(src_node_types)
        dst_node_types = frozenset(dst_node_types)
        return self._match_class(
            self.graph,
            types,
            src_node_types,
            dst_node_types,
            props,
            src_node_props,
            dst_node_props,
        )

    def all(
        self,
        types: Iterable[str] = frozenset(),
        src_node_types: Iterable[str] = frozenset(),
        dst_node_types: Iterable[str] = frozenset(),
        props: Optional[PROPS] = None,
        src_node_props: Optional[PROPS] = None,
        dst_node_props: Optional[PROPS] = None,
    ) -> List[Edge]:
        """获取全部边"""
        types = frozenset(types)
        src_node_types = frozenset(src_node_types)
        dst_node_types = frozenset(dst_node_types)
        return self._match_class(
            self.graph,
            types,
            src_node_types,
            dst_node_types,
            props,
            src_node_props,
            dst_node_props,
        ).all()

    def count(
        self,
        types: Iterable[str] = frozenset(),
        src_node_types: Iterable[str] = frozenset(),
        dst_node_types: Iterable[str] = frozenset(),
        props: Optional[PROPS] = None,
        src_node_props: Optional[PROPS] = None,
        dst_node_props: Optional[PROPS] = None,
    ) -> int:
        types = frozenset(types)
        src_node_types = frozenset(src_node_types)
        dst_node_types = frozenset(dst_node_types)
        """统计边数量"""
        return self._match_class(
            self.graph,
            types,
            src_node_types,
            dst_node_types,
            props,
            src_node_props,
            dst_node_props,
        ).count()
