from .base_match import BaseEdgeMatch, BaseNodeMatch


class Neo4jNodeMatch(BaseNodeMatch):

    def __len__(self):
        """获取所有点"""
        result = self.graph.data_wrapper.as_dict(
            self.graph.client.run(self._query(count=True)).data
        )
        return result["count(n)"]

    def __iter__(self):
        for node in self.graph.data_wrapper.as_nodes(
            self.graph.client.run(self._query()).data
        ):
            yield node


class Neo4jEdgeMatch(BaseEdgeMatch):

    def __len__(self):
        result = self.graph.data_wrapper.as_dict(
            self.graph.client.run(self._query(count=True)).data
        )
        return result["count(r)"]

    def __iter__(self):
        for edge in self.graph.data_wrapper.as_edges(
            self.graph.client.run(self._query()).data
        ):
            yield edge
