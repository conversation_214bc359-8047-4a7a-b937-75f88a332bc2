from typing import Any, Dict, List, Optional

from nebula3.data.DataObject import Node as NeNode
from nebula3.data.DataObject import Null, PathWrapper
from nebula3.data.DataObject import Relationship as NeEdge
from nebula3.data.DataObject import ValueWrapper
from nebula3.data.ResultSet import ResultSet

from yunfu.db.graph.models import (Edge, EdgeProp, EdgeType, Index, IndexTypes, NebulaSpace, Node, NodeProp, NodeType,
                                   Path, Segment)


class NebulaDataWrapper:
    """Nebula 数据处理"""

    @staticmethod
    def as_rows(result: ResultSet) -> List:
        """转换为行"""
        return [[ValueWrapper(_).cast() for _ in row.values] for row in result.rows()]

    @staticmethod
    def as_dict(result: ResultSet) -> Dict[str, Any]:
        """转换为字典"""
        return dict(
            zip(
                result.keys(), [ValueWrapper(_).cast() for _ in result.rows()[0].values]
            )
        )

    @classmethod
    def as_spaces(cls, result: ResultSet) -> List[str]:
        """转换为空间列表"""
        return [_.get_value_by_key("Name").cast() for _ in result]

    @classmethod
    def as_space(cls, result: ResultSet) -> NebulaSpace:
        """转换为空间"""
        return NebulaSpace(
            name=result.column_values("Name")[0].cast(),
            partition_num=result.column_values("Partition Number")[0].cast(),
            replica_factor=result.column_values("Replica Factor")[0].cast(),
            node_id_type=result.column_values("Vid Type")[0].cast(),
            comment=result.column_values("Comment")[0].cast() or "",
        )

    @classmethod
    def as_node_types(cls, result: ResultSet) -> List[str]:
        """转换为多个节点类型"""
        return [_.get_value_by_key("Name").cast() for _ in result]

    @classmethod
    def as_node_type(cls, name: str, result: ResultSet) -> NodeType:
        """转换为节点类型"""
        props = []
        for row in result.rows():
            raw_prop = dict(
                zip(result.keys(), [ValueWrapper(_).cast() for _ in row.values])
            )
            props.append(
                NodeProp(
                    name=raw_prop["Field"],
                    type=raw_prop["Type"],
                    nullable=raw_prop["Null"] == "YES",
                    default=raw_prop["Default"],
                    comment=raw_prop["Comment"],
                )
            )
        return NodeType(
            name=name,
            props=props,
        )  # 拿不到 comment, ttl_duration, ttl_col

    @classmethod
    def as_edge_types(cls, result: ResultSet) -> List[str]:
        """转换为多个边类型"""
        return [_.get_value_by_key("Name").cast() for _ in result]

    @classmethod
    def as_edge_type(cls, name: str, result: ResultSet) -> EdgeType:
        """转换为边类型"""
        props = []
        for row in result.rows():
            raw_prop = dict(
                zip(result.keys(), [ValueWrapper(_).cast() for _ in row.values])
            )
            props.append(
                EdgeProp(
                    name=raw_prop["Field"],
                    type=raw_prop["Type"],
                    nullable=raw_prop["Null"] == "YES",
                    default=raw_prop["Default"],
                    comment=raw_prop["Comment"],
                )
            )
        return EdgeType(
            name=name,
            props=props,
        )  # 拿不到 comment, ttl_duration, ttl_col

    @classmethod
    def as_nodes(cls, result: ResultSet, output: str = "v") -> List[Node]:
        """转换为节点列表"""
        nodes = []
        for record in result.column_values(output):
            nodes.append(cls.ne_node_to_node(record.as_node()))
        return nodes

    @classmethod
    def as_node(cls, result: ResultSet, output: str = "v") -> Optional[Node]:
        """转换为节点"""
        if result.is_empty():
            return None
        ne_node = result.column_values(output)[0].as_node()
        return cls.ne_node_to_node(ne_node)

    @staticmethod
    def ne_node_to_node(ne_node: NeNode) -> Node:
        """转换为节点"""
        props = {}
        for tag in ne_node.tags():
            props.update(ne_node.properties(tag))
        return Node(
            id=ne_node.get_id().cast(),
            types=ne_node.tags(),
            props={
                k: v.cast() for k, v in props.items() if not isinstance(v.cast(), Null)
            },
        )

    @classmethod
    def as_edges(
        cls,
        result: ResultSet,
        output: str = "e",
        src_output: str = "src",
        dst_output: str = "dst",
    ) -> List[Edge]:
        """转换为边列表"""
        edges = []
        for row_num, record in enumerate(result.column_values(output)):
            edge = cls.ne_edge_to_edge(record.as_relationship())
            edge.src_node = cls.ne_node_to_node(
                result.column_values(src_output)[row_num].as_node()
            )
            edge.dst_node = cls.ne_node_to_node(
                result.column_values(dst_output)[row_num].as_node()
            )
            edges.append(edge)
        return edges

    @classmethod
    def as_edge(
        cls,
        result: ResultSet,
        output: str = "e",
        src_output: str = "src",
        dst_output: str = "dst",
    ) -> Optional[Edge]:
        """转换为边"""
        if result.is_empty():
            return None
        ne_edge = result.column_values(output)[0].as_relationship()
        edge = cls.ne_edge_to_edge(ne_edge)
        edge.src_node = cls.ne_node_to_node(
            result.column_values(src_output)[0].as_node()
        )
        edge.dst_node = cls.ne_node_to_node(
            result.column_values(dst_output)[0].as_node()
        )
        return edge

    @staticmethod
    def ne_edge_to_edge(ne_edge: NeEdge) -> Edge:
        """转换为边"""
        edge = Edge(
            src_id=ne_edge.start_vertex_id().cast(),
            dst_id=ne_edge.end_vertex_id().cast(),
            type=ne_edge.edge_name(),
            props={
                k: v.cast()
                for k, v in ne_edge.properties().items()
                if not isinstance(v.cast(), Null)
            },
            rank=ne_edge.ranking(),
        )
        edge.id = edge.props.get("id")
        return edge

    @staticmethod
    def as_index_status(result: ResultSet) -> Dict[str, str]:
        """转换为索引状态"""
        return {
            _.get_value_by_key("Name").cast(): _.get_value_by_key("Index Status").cast()
            for _ in result
        }

    @staticmethod
    def as_indexes(result: ResultSet) -> List[str]:
        """转换为索引列表"""
        return [_.get_value_by_key("Index Name").cast() for _ in result]

    @staticmethod
    def as_index(name: str, index_type: IndexTypes, result: ResultSet) -> Index:
        """转换为索引"""
        for record in result:
            if record.get_value_by_key("Index Name").cast() == name:
                related_to_field = (
                    "By Tag" if index_type == IndexTypes.NEBULA_TAG else "By Edge"
                )
                return Index(
                    name=name,
                    related_to=record.get_value_by_key(related_to_field).cast(),
                    type=index_type,
                    props=record.get_value_by_key("Columns").cast(),
                )
        raise ValueError(f"未找到索引: {name}")

    @classmethod
    def as_paths(cls, result: ResultSet, output: str = "p") -> List[Path]:
        """转换为路径列表"""
        paths = []
        for record in result.column_values(output):
            paths.append(cls.as_path(record.as_path()))
        return paths

    @staticmethod
    def as_path(result: PathWrapper) -> Path:
        """转换为路径"""
        nodes = []
        edges = []
        segments = []
        for node in result.nodes():
            nodes.append(NebulaDataWrapper.ne_node_to_node(node))
        for segment in result.segments():
            start = NebulaDataWrapper.ne_node_to_node(segment.start_node)
            end = NebulaDataWrapper.ne_node_to_node(segment.end_node)
            edge = NebulaDataWrapper.ne_edge_to_edge(segment.relationship)
            edge.src_node = start
            edge.dst_node = end
            edges.append(edge)
            segments.append(Segment(start=start, end=end, edge=edge))
        return Path(nodes=nodes, edges=edges, segments=segments)
