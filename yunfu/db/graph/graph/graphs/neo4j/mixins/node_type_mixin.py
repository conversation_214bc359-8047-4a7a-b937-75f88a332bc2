from typing import List

from yunfu.db.graph.clients.neo4j_client import Neo4jClient
from yunfu.db.graph.core.exceptions import NotExistedError
from yunfu.db.graph.graph.data_wrappers.py2neo_data_wrapper import Py2neoDataWrapper
from yunfu.db.graph.graph.graphs.base.mixins import BaseEdgeTypeReadonlyMixin, BaseNodeTypeMixin
from yunfu.db.graph.graph.templates.cypher.cypher_template import CypherTemplate
from yunfu.db.graph.models.node import NodeType


class Neo4jNodeTypeReadonlyMixin(BaseEdgeTypeReadonlyMixin):
    client: Neo4jClient
    template: CypherTemplate
    data_wrapper: Py2neoDataWrapper

    @property
    def node_types(self) -> List[str]:
        node_types = self.data_wrapper.as_node_types(
            self.client.run(self.template.get_node_types(self.space.name)).data
        )
        node_types.remove(self.space.name)
        node_types.remove(self.version.name)
        return node_types

    def get_node_type(self, node_type_name: str) -> NodeType:
        """获取节点类型（Neo4j中的label，nebula中的tag）"""
        if not self.has_node_type(node_type_name):
            raise NotExistedError("Node Type", node_type_name)
        return NodeType(name=node_type_name)

    def has_node_type(self, node_type_name: str) -> bool:
        """判断节点类型是否存在"""
        return node_type_name in self.node_types


class Neo4jNodeTypeMixin(BaseNodeTypeMixin):
    client: Neo4jClient
    template: CypherTemplate
    data_wrapper: Py2neoDataWrapper

    def create_node_type(self, node_type: NodeType, wait: bool = False) -> None:
        """创建节点类型"""
        raise NotImplementedError

    def create_node_types(self, node_types: List[NodeType], wait: bool = False) -> None:
        """批量创建节点类型"""
        raise NotImplementedError

    def alter_node_type(self, node_type: NodeType, wait: bool = False) -> None:
        """修改节点类型"""
        raise NotImplementedError

    def drop_node_type(self, node_type_name: str) -> None:
        """删除节点类型"""
        raise NotImplementedError
