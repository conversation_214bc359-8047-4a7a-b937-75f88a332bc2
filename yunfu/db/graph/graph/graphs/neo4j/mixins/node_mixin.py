from typing import Any, Dict, List, Optional

from yunfu.db.graph.clients.neo4j_client import Neo4jClient
from yunfu.db.graph.core.exceptions import NotExistedError
from yunfu.db.graph.graph.data_wrappers.py2neo_data_wrapper import Py2neoDataWrapper
from yunfu.db.graph.graph.graphs.base.mixins import BaseNodeMixin, BaseNodeReadonlyMixin
from yunfu.db.graph.graph.matchers.neo4j_matcher import Neo4jNodeMatcher
from yunfu.db.graph.graph.templates.cypher.cypher_template import CypherTemplate
from yunfu.db.graph.models.node import Node


class Neo4jNodeReadonlyMixin(BaseNodeReadonlyMixin):
    client: Neo4jClient
    template: CypherTemplate
    data_wrapper: Py2neoDataWrapper

    @property
    def nodes(self) -> Neo4jNodeMatcher:
        return Neo4jNodeMatcher(self)

    def get_node(self, node_id: str, raise_exception: bool = True) -> Optional[Node]:
        """获取节点"""
        result = self.client.run(
            self.template.get_node(self.space.name, node_id, self.version.name)
        )
        if not result.data.preview():
            if raise_exception:
                raise NotExistedError("Node", node_id)
            return None
        node = self.data_wrapper.as_node(result.data)
        node.types.remove(self.space.name)
        node.types.remove(self.version.name)
        return node


class Neo4jNodeMixin(BaseNodeMixin):
    client: Neo4jClient
    template: CypherTemplate
    data_wrapper: Py2neoDataWrapper

    def insert_node(self, node: Node) -> Node:
        """插入节点"""
        self.insert_nodes([node])
        return self.get_node(node.id)

    def insert_nodes(self, nodes: List[Node]) -> None:
        """插入多个点"""
        if not nodes:
            return
        self.client.run(
            self.template.insert_nodes(self.space.name, nodes, self.version.name)
        )

    def delete_node(self, node_id: str, with_edge: bool = True) -> None:
        """删除节点"""
        self.delete_nodes([node_id], with_edge)

    def delete_nodes(self, node_ids: List[str], with_edge: bool = True) -> None:
        """删除多个节点"""
        if not node_ids:
            return
        self.client.run(
            self.template.delete_nodes(self.space.name, node_ids, with_edge)
        )

    def update_node(self, node_id: str, props: Dict[str, Any]) -> Node:
        """更新节点"""
        self.client.run(self.template.update_node(self.space.name, node_id, props))
        return self.get_node(node_id)

    def upsert_nodes(self, nodes: List[Node], primary_key: str) -> None:
        """更新或插入节点"""
        if not nodes:
            return
        node_dict: Dict[str, Node] = {}
        remain_nodes = []
        for node in nodes:
            if node.props.get(primary_key):
                node_dict[node.props[primary_key]] = node
            else:
                remain_nodes.append(node)
        existed_nodes = self.nodes.match(
            props=[(primary_key, "in", list(node_dict.keys()))]
        ).all()
        for node in existed_nodes:
            self.update_node(node.id, node_dict[node.props[primary_key]].props)
            node_dict.pop(node.props[primary_key])
        remain_nodes.extend(list(node_dict.values()))
        if remain_nodes:
            self.insert_nodes(remain_nodes)
