from typing import Any, Dict, List, Optional

from yunfu.db.graph.clients.neo4j_client import Neo4jClient
from yunfu.db.graph.core.exceptions import NotExistedError
from yunfu.db.graph.graph.data_wrappers.py2neo_data_wrapper import Py2neoDataWrapper
from yunfu.db.graph.graph.graphs.base.mixins import BaseEdgeMixin, BaseEdgeReadonlyMixin
from yunfu.db.graph.graph.matchers.neo4j_matcher import Neo4jEdgeMatcher
from yunfu.db.graph.graph.templates.cypher.cypher_template import CypherTemplate
from yunfu.db.graph.models import Edge


class Neo4jEdgeReadonlyMixin(BaseEdgeReadonlyMixin):
    client: Neo4jClient
    template: CypherTemplate
    data_wrapper: Py2neoDataWrapper

    @property
    def edges(self) -> Neo4jEdgeMatcher:
        return Neo4jEdgeMatcher(self)

    def get_edge(self, edge_id: str, raise_exception: bool = True) -> Optional[Edge]:
        """获取边"""
        result = self.client.run(
            self.template.get_edge(self.space.name, edge_id, self.version.name)
        )
        if not result.data.preview():
            if raise_exception:
                raise NotExistedError("Edge", edge_id)
            return None
        return self.data_wrapper.as_edge(result.data)


class Neo4jEdgeMixin(BaseEdgeMixin):
    client: Neo4jClient
    template: CypherTemplate
    data_wrapper: Py2neoDataWrapper

    def insert_edge(self, edge: Edge) -> Edge:
        """插入边"""
        self.client.run(
            self.template.insert_edge(self.space.name, edge, self.version.name)
        )
        return self.get_edge(edge.id)

    def insert_edges(self, edges: List[Edge]) -> None:
        """插入多条边"""
        for edge in edges:
            self.client.run(
                self.template.insert_edge(self.space.name, edge, self.version.name)
            )

    def delete_edge(self, edge_id: str, **kwargs: Any) -> None:
        """删除边"""
        self.client.run(self.template.delete_edge(self.space.name, edge_id, **kwargs))

    def update_edge(self, edge_id: str, props: Dict[str, Any], **kwargs: Any) -> Edge:
        """更新边属性"""
        self.client.run(
            self.template.update_edge(self.space.name, edge_id, props, **kwargs)
        )
        return self.get_edge(edge_id)

    def upsert_edges(self, edges: List[Edge], primary_key: str = "name") -> None:
        """更新或插入边"""
        if not edges:
            return
        edge_dict: Dict[str, Edge] = {}
        remain_edges = []
        for edge in edges:
            if edge.props.get(primary_key):
                key = f"{edge.src_id}-{edge.dst_id}-{edge.props[primary_key]}"
                edge_dict[key] = edge
            else:
                remain_edges.append(edge)
        prop_values = [
            edge.props[primary_key] for edge in edges if edge.props.get(primary_key)
        ]
        existed_edges = self.edges.match(
            props=[(primary_key, "in", prop_values)],
            src_node_props=[("_id", "in", [edge.src_id for edge in edges])],
            dst_node_props=[("_id", "in", [edge.dst_id for edge in edges])],
        ).all()
        for edge in existed_edges:
            key = f"{edge.src_id}-{edge.dst_id}-{edge.props[primary_key]}"
            if key in edge_dict:
                self.update_edge(edge.id, edge_dict[key].props)
                edge_dict.pop(key)
        remain_edges.extend(list(edge_dict.values()))
        if remain_edges:
            self.insert_edges(remain_edges)
