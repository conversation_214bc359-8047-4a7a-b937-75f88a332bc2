from typing import List

from yunfu.db.graph.graph.graphs.base.mixins import BaseIndexMixin, BaseIndexReadonlyMixin
from yunfu.db.graph.models.graph import Constraint, Index, IndexTypes


class Neo4jIndexReadonlyMixin(BaseIndexReadonlyMixin):

    @property
    def indexes(self) -> List[str]:
        raise NotImplementedError

    @property
    def constraints(self) -> List[str]:
        raise NotImplementedError

    def get_index(self, name: str, index_type: IndexTypes) -> Index:
        """获取索引"""
        raise NotImplementedError

    def has_index(self, name: str, index_type: IndexTypes) -> Index:
        """判断索引是否存在"""
        raise NotImplementedError


class Neo4jIndexMixin(BaseIndexMixin):

    def create_index(self, index: Index, wait: bool = True) -> None:
        """创建索引"""
        raise NotImplementedError

    def rebuild_index(
        self, name: str, index_type: IndexTypes, wait: bool = True, timeout: int = 30
    ) -> None:
        raise NotImplementedError

    def drop_index(self, index_name: str, index_type: IndexTypes) -> None:
        """删除索引"""
        raise NotImplementedError

    def create_constraint(self, constraint: Constraint, wait: bool = True) -> None:
        """创建约束"""
        raise NotImplementedError

    def drop_constraint(self, constraint_name: str) -> None:
        """删除约束"""
        raise NotImplementedError
