from typing import List

from yunfu.db.graph.clients.neo4j_client import Neo4j<PERSON>lient
from yunfu.db.graph.core.exceptions import NotExistedError
from yunfu.db.graph.graph.data_wrappers.py2neo_data_wrapper import Py2neoDataWrapper
from yunfu.db.graph.graph.graphs.base.mixins import BaseEdgeTypeMixin, BaseEdgeTypeReadonlyMixin
from yunfu.db.graph.graph.templates.cypher.cypher_template import CypherTemplate
from yunfu.db.graph.models.edge import EdgeType


class Neo4jEdgeTypeReadonlyMixin(BaseEdgeTypeReadonlyMixin):
    client: Neo4jClient
    template: CypherTemplate
    data_wrapper: Py2neoDataWrapper

    @property
    def edge_types(self) -> List[str]:
        return self.data_wrapper.as_edge_types(
            self.client.run(self.template.get_edge_types(self.space.name)).data
        )

    def get_edge_type(self, edge_type_name: str) -> EdgeType:
        """获取边类型"""
        if not self.has_edge_type(edge_type_name):
            raise NotExistedError("Edge Type", edge_type_name)
        return EdgeType(name=edge_type_name)

    def has_edge_type(self, edge_type_name: str) -> bool:
        """判断边类型是否存在"""
        return edge_type_name in self.edge_types


class Neo4jEdgeTypeMixin(BaseEdgeTypeMixin):
    client: Neo4jClient
    template: CypherTemplate
    data_wrapper: Py2neoDataWrapper

    def create_edge_type(self, edge_type: EdgeType, wait: bool = False) -> None:
        """创建边类型"""
        raise NotImplementedError

    def create_edge_types(self, edge_types: List[EdgeType], wait: bool = False) -> None:
        """批量创建边类型"""
        raise NotImplementedError

    def alter_edge_type(self, edge_type: EdgeType, wait: bool = False) -> None:
        """修改边类型"""
        raise NotImplementedError

    def drop_edge_type(self, edge_type_name: str) -> None:
        """删除边类型"""
        raise NotImplementedError
