from typing import List, Union

from yunfu.db.graph.core.logging import get_logger
from yunfu.db.graph.graph.data_wrappers.py2neo_data_wrapper import Py2neoDataWrapper
from yunfu.db.graph.graph.graphs.base import BaseGraph, BaseReadonlyGraph
from yunfu.db.graph.graph.services.neo4j_service import Neo4jGraphService
from yunfu.db.graph.graph.templates.cypher.cypher_template import CypherTemplate
from yunfu.db.graph.models import Edge, Node, Version

from . import mixins

logger = get_logger(__name__)


class Neo4jReadonlyGraph(
    BaseReadonlyGraph,
    mixins.Neo4jNodeReadonlyMixin,
    mixins.Neo4jEdgeReadonlyMixin,
    mixins.Neo4jNodeTypeReadonlyMixin,
    mixins.Neo4jEdgeTypeReadonlyMixin,
    mixins.Neo4jIndexReadonlyMixin,
):
    template = CypherTemplate
    data_wrapper = Py2neoDataWrapper

    @property
    def service(self) -> Neo4jGraphService:
        return Neo4jGraphService(self)


class Neo4jGraph(
    BaseGraph,
    Neo4jReadonlyGraph,
    mixins.Neo4jNodeMixin,
    mixins.Neo4jEdgeMixin,
    mixins.Neo4jNodeTypeMixin,
    mixins.Neo4jEdgeTypeMixin,
    mixins.Neo4jIndexMixin,
    mixins.Neo4jVersionMixin,
):

    def insert(self, items: Union[Node, Edge, List[Node], List[Edge]]) -> None:
        """创建节点/边

        :param items: 点/边/点集/边集
        :type items: Node/Edge/List[Node]/List[Edge]
        """
        if isinstance(items, Node):
            self.insert_node(items)
        elif isinstance(items, Edge):
            self.insert_edge(items)
        elif isinstance(items, list):
            self.insert_nodes(list(filter(lambda i: isinstance(i, Node), items)))  # type: ignore
            self.insert_edges(list(filter(lambda i: isinstance(i, Edge), items)))  # type: ignore
        else:
            raise TypeError(f"Unsupported type: {type(items)}")

    def get_history_graph(self, version: str) -> Neo4jReadonlyGraph:
        """获取历史版本图谱"""
        return Neo4jReadonlyGraph(
            self.space, Version(name=version), self.client, self.config
        )
