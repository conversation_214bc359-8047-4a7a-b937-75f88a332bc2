from typing import List, Union

from yunfu.db.graph.core.logging import get_logger
from yunfu.db.graph.graph.data_wrappers.nebula_data_wrapper import NebulaDataWrapper
from yunfu.db.graph.graph.graphs.base import BaseGraph, BaseReadonlyGraph
from yunfu.db.graph.graph.services.nebula_service import NebulaGraphService
from yunfu.db.graph.graph.templates.ngql.ngql_template import NGQLTemplate
from yunfu.db.graph.models import Edge, Node, Version

from . import mixins

logger = get_logger(__name__)


class NebulaReadonlyGraph(
    BaseReadonlyGraph,
    mixins.NebulaNodeReadonlyMixin,
    mixins.NebulaEdgeReadonlyMixin,
    mixins.NebulaNodeTypeReadonlyMixin,
    mixins.NebulaEdgeTypeReadonlyMixin,
    mixins.NebulaIndexReadonlyMixin,
):
    template = NGQLTemplate
    data_wrapper = NebulaDataWrapper

    @property
    def service(self) -> NebulaGraphService:
        return NebulaGraphService(self)


class NebulaGraph(
    BaseGraph,
    NebulaReadonlyGraph,
    mixins.NebulaNodeMixin,
    mixins.NebulaEdgeMixin,
    mixins.NebulaNodeTypeMixin,
    mixins.NebulaEdgeTypeMixin,
    mixins.NebulaIndexMixin,
    mixins.NebulaVersionMixin,
):

    def insert(self, items: Union[Node, Edge, List[Node], List[Edge]]) -> None:
        """创建节点/边

        :param items: 点/边/点集/边集
        :type items: Node/Edge/List[Node]/List[Edge]
        """
        if isinstance(items, Node):
            self.insert_node(items)
        elif isinstance(items, Edge):
            self.insert_edge(items)
        elif isinstance(items, list):
            self.insert_nodes(list(filter(lambda i: isinstance(i, Node), items)))  # type: ignore
            self.insert_edges(list(filter(lambda i: isinstance(i, Edge), items)))  # type: ignore
        else:
            raise TypeError(f"Unsupported type: {type(items)}")

    def get_history_graph(self, version: str) -> NebulaReadonlyGraph:
        """获取历史版本图谱"""
        return NebulaReadonlyGraph(
            self.space, Version(name=version), self.client, self.config
        )
