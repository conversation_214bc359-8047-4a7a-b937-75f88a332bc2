from typing import Any, Dict, List, Optional

from yunfu.db.graph.clients.nebula_client import NebulaClient
from yunfu.db.graph.core.exceptions import NotExistedError
from yunfu.db.graph.graph.data_wrappers.nebula_data_wrapper import NebulaDataWrapper
from yunfu.db.graph.graph.graphs.base.mixins import BaseEdgeMixin, BaseEdgeReadonlyMixin
from yunfu.db.graph.graph.matchers.nebula_matcher import NebulaEdgeMatcher
from yunfu.db.graph.graph.templates.ngql.ngql_template import NGQLTemplate
from yunfu.db.graph.models import Edge


class NebulaEdgeReadonlyMixin(BaseEdgeReadonlyMixin):
    client: NebulaClient
    template: NGQLTemplate
    data_wrapper: NebulaDataWrapper

    @property
    def edges(self) -> NebulaEdgeMatcher:
        return NebulaEdgeMatcher(graph=self)

    def get_edge(self, edge_id: str, raise_exception: bool = True) -> Optional[Edge]:
        """获取边"""
        result = self.client.run(
            self.template.get_edge(self.space.name, edge_id, self.version.name)
        )
        edge = self.data_wrapper.as_edge(result.data)
        if raise_exception and not edge:
            raise NotExistedError("Edge", f"id => {edge_id}")
        return edge


class NebulaEdgeMixin(BaseEdgeMixin):
    client: NebulaClient
    template: NGQLTemplate
    data_wrapper: NebulaDataWrapper

    def insert_edge(self, edge: Edge) -> Edge:
        """插入多条边"""
        self.insert_edges([edge])
        return self.get_edge(edge.id)

    def insert_edges(self, edges: List[Edge]) -> None:
        """插入边"""
        type2edges: Dict[str, List[Edge]] = {}
        for edge in edges:
            type2edges.setdefault(edge.type, []).append(edge)
        for edge_type, edges in type2edges.items():
            props = [_.name for _ in self.get_edge_type(edge_type).props]
            self.client.run(
                self.template.insert_edges(edges, self.version.name, edge_type, props)
            )

    def delete_edge(self, edge_id: str) -> None:
        """删除边"""
        edge = self.get_edge(edge_id)
        self.client.run(self.template.delete_edge(edge))

    def update_edge(self, edge_id: str, props: Dict[str, Any]) -> Edge:
        """更新边"""
        edge = self.get_edge(edge_id)
        self.client.run(self.template.update_edge(edge, props))
        return self.get_edge(edge_id)

    def upsert_edges(self, edges: List[Edge], primary_key: str = "name") -> None:
        """更新或插入边"""
        if not edges:
            return
        edge_dict: Dict[str, Edge] = {}
        remain_edges = []
        for edge in edges:
            if edge.props.get(primary_key):
                key = f"{edge.src_id}-{edge.dst_id}-{edge.props[primary_key]}"
                edge_dict[key] = edge
            else:
                remain_edges.append(edge)
        prop_values = [
            edge.props[primary_key] for edge in edges if edge.props.get(primary_key)
        ]
        existed_edges = self.edges.match(
            props=[(primary_key, "in", prop_values)],
            src_node_props=[("id", "in", [edge.src_id for edge in edges])],
            dst_node_props=[("id", "in", [edge.dst_id for edge in edges])],
        ).all()
        for edge in existed_edges:
            key = f"{edge.src_id}-{edge.dst_id}-{edge.props[primary_key]}"
            if key in edge_dict:
                self.update_edge(edge.id, edge_dict[key].props)
                edge_dict.pop(key)
        remain_edges.extend(list(edge_dict.values()))
        if remain_edges:
            self.insert_edges(remain_edges)
