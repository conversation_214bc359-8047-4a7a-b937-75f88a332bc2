from typing import Any, Dict, List, Optional

from yunfu.db.graph.clients.nebula_client import NebulaClient
from yunfu.db.graph.core.exceptions import NotExistedError
from yunfu.db.graph.graph.data_wrappers.nebula_data_wrapper import NebulaDataWrapper
from yunfu.db.graph.graph.graphs.base.mixins import BaseNodeMixin, BaseNodeReadonlyMixin
from yunfu.db.graph.graph.matchers.nebula_matcher import NebulaNodeMatcher
from yunfu.db.graph.graph.templates.ngql.ngql_template import NGQLTemplate
from yunfu.db.graph.models.node import Node


class NebulaNodeReadonlyMixin(BaseNodeReadonlyMixin):
    client: NebulaClient
    template: NGQLTemplate
    data_wrapper: NebulaDataWrapper

    @property
    def nodes(self) -> NebulaNodeMatcher:
        return NebulaNodeMatcher(graph=self)

    def get_node(self, node_id: str, raise_exception: bool = True) -> Optional[Node]:
        """获取节点"""
        result = self.client.run(
            self.template.get_node(self.space.name, node_id, self.version.name)
        )
        node = self.data_wrapper.as_node(result.data)
        if raise_exception and node is None:
            raise NotExistedError("Node", f"id => {node_id}")
        node.types.remove(self.space.name)
        node.types.remove(self.version.name)
        return node


class NebulaNodeMixin(BaseNodeMixin):
    client: NebulaClient
    template: NGQLTemplate
    data_wrapper: NebulaDataWrapper

    def insert_node(self, node: Node) -> Node:
        """插入节点"""
        self.insert_nodes([node])
        return self.get_node(node.id)

    def insert_nodes(self, nodes: List[Node]) -> None:
        """插入节点"""
        batches: Dict[tuple, List[Node]] = {}
        for node in nodes:
            # 将要插入的节点按照类型和属性分组
            key = tuple(node.types + list(node.props.keys()))
            if key in batches:
                batches[key].append(node)
            else:
                batches[key] = [node]
        for nodes in batches.values():
            self.client.run(
                self.template.insert_nodes(self.space.name, nodes, self.version.name)
            )

    def delete_node(self, node_id: str, with_edge: bool = True) -> None:
        """删除节点"""
        self.delete_nodes([node_id], with_edge)

    def delete_nodes(self, node_ids: List[str], with_edge: bool = True) -> None:
        """删除多个节点"""
        self.client.run(self.template.delete_nodes(node_ids, with_edge))

    def update_node(self, node_id: str, props: Dict[str, Any]) -> Node:
        """更新节点"""
        self.client.run(self.template.update_node(node_id, self.space.name, props))
        return self.get_node(node_id)

    def upsert_nodes(self, nodes: List[Node], primary_key: str) -> None:
        """更新或插入节点"""
        if not nodes:
            return
        node_dict: Dict[str, Node] = {}
        remain_nodes = []
        for node in nodes:
            if node.props.get(primary_key):
                node_dict[node.props[primary_key]] = node
            else:
                remain_nodes.append(node)
        existed_nodes = self.nodes.match(
            props=[(primary_key, "in", list(node_dict.keys()))]
        ).all()
        for node in existed_nodes:
            self.update_node(node.id, node_dict[node.props[primary_key]].props)
            node_dict.pop(node.props[primary_key])
        remain_nodes.extend(list(node_dict.values()))
        if remain_nodes:
            self.insert_nodes(remain_nodes)
