from typing import List

from yunfu.db.graph.clients.nebula_client import NebulaClient
from yunfu.db.graph.core.constants import NebulaMessages
from yunfu.db.graph.graph.data_wrappers.nebula_data_wrapper import NebulaDataWrapper
from yunfu.db.graph.graph.graphs.base.mixins import BaseEdgeTypeMixin, BaseEdgeTypeReadonlyMixin
from yunfu.db.graph.graph.templates.ngql.ngql_template import NGQLTemplate
from yunfu.db.graph.models.edge import EdgeType


class NebulaEdgeTypeReadonlyMixin(BaseEdgeTypeReadonlyMixin):
    client: NebulaClient
    template: NGQLTemplate
    data_wrapper: NebulaDataWrapper

    @property
    def edge_types(self) -> List[str]:
        """获取所有边类型"""
        return self.data_wrapper.as_edge_types(
            self.client.run(self.template.get_edge_types(self.space.name)).data
        )

    def get_edge_type(self, edge_type_name: str) -> EdgeType:
        """获取边类型"""
        result = self.client.run(
            self.template.get_edge_type(self.space.name, edge_type_name)
        )
        return self.data_wrapper.as_edge_type(edge_type_name, result.data)

    def has_edge_type(self, edge_type_name: str) -> bool:
        """判断边类型是否存在"""
        result = self.client.run(
            self.template.get_edge_type(self.space.name, edge_type_name),
            raise_exception=False,
        )
        if NebulaMessages.EDGE_NOT_EXISTED in result.message:
            return False
        return result.is_succeeded


class NebulaEdgeTypeMixin(BaseEdgeTypeMixin):
    client: NebulaClient
    template: NGQLTemplate
    data_wrapper: NebulaDataWrapper

    def create_edge_type(self, edge_type: EdgeType, wait: bool = True) -> None:
        """创建边类型"""
        self.client.run(self.template.create_edge_type(self.space.name, edge_type))
        if wait:
            self.client.wait()

    def create_edge_types(self, edge_types: List[EdgeType], wait: bool = False) -> None:
        """批量创建边类型"""
        for edge_type in edge_types:
            self.create_edge_type(edge_type, wait=False)
        if wait:
            self.client.wait()

    def alter_edge_type(self, edge_type: EdgeType, wait: bool = True) -> None:
        """修改边类型"""
        old_edge_type = self.get_edge_type(edge_type.name)
        self.client.run(
            self.template.alter_edge_type(self.space.name, old_edge_type, edge_type)
        )
        if wait:
            self.client.wait()

    def drop_edge_type(self, edge_type_name: str) -> None:
        """删除边类型"""
        self.client.run(self.template.drop_edge_type(self.space.name, edge_type_name))
