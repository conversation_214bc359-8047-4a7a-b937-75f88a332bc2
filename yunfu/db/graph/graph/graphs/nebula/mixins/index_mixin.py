import time
from typing import List

from yunfu.db.graph.clients.nebula_client import NebulaClient
from yunfu.db.graph.core.constants import NebulaMessages
from yunfu.db.graph.core.logging import get_logger
from yunfu.db.graph.graph.data_wrappers.nebula_data_wrapper import NebulaDataWrapper
from yunfu.db.graph.graph.graphs.base.mixins import BaseIndexMixin, BaseIndexReadonlyMixin
from yunfu.db.graph.graph.templates.ngql.ngql_template import NGQLTemplate
from yunfu.db.graph.models.graph import Constraint, Index, IndexTypes

logger = get_logger(__name__)


class NebulaIndexReadonlyMixin(BaseIndexReadonlyMixin):
    client: NebulaClient
    template: NGQLTemplate
    data_wrapper: NebulaDataWrapper

    @property
    def indexes(self) -> List[str]:
        """获取所有索引配置"""
        node_type_indexes = self.data_wrapper.as_indexes(
            self.client.run(self.template.get_indexes(IndexTypes.NEBULA_TAG)).data
        )
        edge_type_indexes = self.data_wrapper.as_indexes(
            self.client.run(self.template.get_indexes(IndexTypes.NEBULA_EDGE)).data
        )
        indexes = node_type_indexes + edge_type_indexes
        indexes.remove(self.space.name)
        return indexes

    @property
    def constraints(self) -> List[str]:
        """获取所有约束配置"""
        raise NotImplementedError("暂不支持")

    def get_index(self, name: str, index_type: IndexTypes) -> Index:
        """获取索引"""
        return self.data_wrapper.as_index(
            name,
            index_type,
            self.client.run(self.template.get_indexes(index_type)).data,
        )

    def has_index(self, name: str, index_type: IndexTypes) -> Index:
        """判断索引是否存在"""
        result = self.client.run(
            self.template.get_index(name, index_type),
            raise_exception=False,
        )
        if NebulaMessages.INDEX_NOT_EXISTED in result.message:
            return False
        return result.is_succeeded


class NebulaIndexMixin(BaseIndexMixin):
    client: NebulaClient
    template: NGQLTemplate
    data_wrapper: NebulaDataWrapper

    def create_indexes(self, indexes: List[Index], wait: bool = True) -> None:
        """创建索引"""
        for index in indexes:
            self.create_index(index, wait=False)
        if wait:
            self.client.wait()

    def create_index(self, index: Index, wait: bool = True) -> None:
        """创建索引"""
        self.client.run(self.template.create_index(index))
        if wait:
            self.client.wait()

    def check_index_status(
        self, name: str, index_type: IndexTypes, status: str = "FINISHED"
    ) -> bool:
        """判断索引是否创建完成"""
        result = self.client.run(
            self.template.get_index_status(index_type),
        )
        name2status = self.data_wrapper.as_index_status(result.data)
        logger.info(f"Index status: {name2status}")
        if name2status.get(name) == status:
            return True
        return False

    def rebuild_index(
        self, name: str, index_type: IndexTypes, wait: bool = True, timeout: int = 30
    ) -> None:
        self.client.run(self.template.rebuild_index(name, index_type))
        if wait:
            secs = 0
            while not self.check_index_status(name, index_type, "FINISHED"):
                time.sleep(1)
                secs += 1
                if secs >= timeout:
                    raise TimeoutError(f"Rebuild index {name} timeout")

    def drop_index(self, name: str, index_type: IndexTypes) -> None:
        """删除索引"""
        self.client.run(self.template.drop_index(name, index_type))

    def create_constraint(self, constraint: Constraint, wait: bool = True) -> None:
        """创建约束"""
        raise NotImplementedError

    def drop_constraint(self, name: str) -> None:
        """删除约束"""
        raise NotImplementedError
