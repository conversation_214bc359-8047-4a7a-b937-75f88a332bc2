from typing import List

from yunfu.db.graph.clients.nebula_client import NebulaClient
from yunfu.db.graph.core.constants import NebulaMessages
from yunfu.db.graph.graph.data_wrappers.nebula_data_wrapper import NebulaDataWrapper
from yunfu.db.graph.graph.graphs.base.mixins import BaseEdgeTypeReadonlyMixin, BaseNodeTypeMixin
from yunfu.db.graph.graph.templates.ngql.ngql_template import NGQLTemplate
from yunfu.db.graph.models.node import NodeType


class NebulaNodeTypeReadonlyMixin(BaseEdgeTypeReadonlyMixin):
    client: NebulaClient
    template: NGQLTemplate
    data_wrapper: NebulaDataWrapper

    @property
    def node_types(self) -> List[str]:
        """获取所有节点类型"""
        node_types = self.data_wrapper.as_node_types(
            self.client.run(self.template.get_node_types(self.space.name)).data
        )
        remove_types = (self.space.name, self.version.name)
        return list(filter(lambda x: x not in remove_types, node_types))

    def get_node_type(self, node_type_name: str) -> NodeType:
        """获取节点类型（Neo4j中的label，nebula中的tag）"""
        result = self.client.run(
            self.template.get_node_type(self.space.name, node_type_name)
        )
        return self.data_wrapper.as_node_type(node_type_name, result.data)

    def has_node_type(self, node_type_name: str) -> bool:
        """判断节点类型是否存在"""
        result = self.client.run(
            self.template.get_node_type(self.space.name, node_type_name),
            raise_exception=False,
        )
        if NebulaMessages.TAG_NOT_EXISTED in result.message:
            return False
        return result.is_succeeded


class NebulaNodeTypeMixin(BaseNodeTypeMixin):
    client: NebulaClient
    template: NGQLTemplate
    data_wrapper: NebulaDataWrapper

    def create_node_type(self, node_type: NodeType, wait: bool = True) -> None:
        """创建节点类型"""
        self.client.run(self.template.create_node_type(self.space.name, node_type))
        if wait:
            self.client.wait()

    def create_node_types(self, node_types: List[NodeType], wait: bool = True) -> None:
        """批量创建节点类型"""
        for node_type in node_types:
            self.create_node_type(node_type, wait=False)
        if wait:
            self.client.wait()

    def alter_node_type(self, node_type: NodeType, wait: bool = True) -> None:
        """修改节点类型"""
        old_node_type = self.get_node_type(node_type.name)
        self.client.run(
            self.template.alter_node_type(self.space.name, old_node_type, node_type)
        )
        if wait:
            self.client.wait()

    def drop_node_type(self, name: str) -> None:
        """删除节点类型"""
        self.client.run(self.template.drop_node_type(self.space.name, name))
