from abc import ABC, abstractmethod

from yunfu.db.graph.clients.base_client import BaseClient
from yunfu.db.graph.core.models.config import Config
from yunfu.db.graph.graph.services.base_service import BaseGraphService
from yunfu.db.graph.graph.templates import Template
from yunfu.db.graph.models.graph import Space, Version


class BaseReadonlyGraph(ABC):
    template: Template  # 模板类

    def __init__(
        self, space: Space, version: Version, client: BaseClient, config: Config
    ) -> None:
        self.space = space
        self.version = version
        self.client = client
        self.config = config

    @property
    @abstractmethod
    def service(self) -> BaseGraphService:
        """获取图谱服务

        :return: 图谱服务
        :rtype: BaseGraphService
        """
        raise NotImplementedError


class BaseGraph(BaseReadonlyGraph):

    def __init__(self, space: Space, client: BaseClient, config: Config) -> None:
        self.space = space
        self.client = client
        self.config = config
        self.version = Version(name=config.version.c)

    @abstractmethod
    def get_history_graph(self, version: str) -> BaseReadonlyGraph:
        """获取历史版本图谱

        :param version: 版本名
        :type version: str
        :return: 指定版本图谱
        :rtype: YFRetrieveGraph
        """
        raise NotImplementedError
