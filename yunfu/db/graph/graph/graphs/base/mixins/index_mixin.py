from abc import ABC, abstractmethod
from typing import List

from yunfu.db.graph.models.graph import Constraint, Index, IndexTypes


class BaseIndexReadonlyMixin(ABC):

    @property
    @abstractmethod
    def indexes(self) -> List[str]:
        """获取所有索引配置

        :return: 全部索引
        :rtype: List[dict]
        """
        raise NotImplementedError

    @property
    @abstractmethod
    def constraints(self) -> List[str]:
        """获取所有约束配置

        :return: 全部约束
        :rtype: List[dict]
        """
        raise NotImplementedError

    @abstractmethod
    def get_index(self, name: str, index_type: IndexTypes) -> Index:
        """获取索引

        :param name: 索引名称
        :type name: str
        """
        raise NotImplementedError

    @abstractmethod
    def has_index(self, name: str, index_type: IndexTypes) -> bool:
        """判断索引是否存在

        :param name: 索引名称
        :type name: str
        """
        raise NotImplementedError


class BaseIndexMixin(ABC):

    @abstractmethod
    def create_index(self, index: Index, wait: bool = False) -> None:
        """创建索引

        :param name: 索引
        :type name: Index
        """
        raise NotImplementedError

    @abstractmethod
    def rebuild_index(
        self, name: str, index_type: IndexTypes, wait: bool = False
    ) -> None:
        """重建索引

        :param name: 索引
        :type name: Index
        """
        raise NotImplementedError

    @abstractmethod
    def drop_index(self, name: str, index_type: IndexTypes) -> None:
        """删除索引

        :param name: 索引名称
        :type name: str
        """
        raise NotImplementedError

    @abstractmethod
    def create_constraint(self, constraint: Constraint, wait: bool = False) -> None:
        """创建约束

        :param name: 约束
        :type name: Constraint
        """
        raise NotImplementedError

    @abstractmethod
    def drop_constraint(self, name: str) -> None:
        """删除约束

        :param name: 约束名称
        :type name: str
        """
        raise NotImplementedError
