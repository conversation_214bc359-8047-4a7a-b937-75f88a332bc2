from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from yunfu.db.graph.graph.matchers.base_matcher import BaseEdgeMatcher
from yunfu.db.graph.models import Edge


class BaseEdgeReadonlyMixin(ABC):

    @property
    @abstractmethod
    def edges(self) -> BaseEdgeMatcher:
        """获取edge matcher

        :return: edge matcher
        :rtype: BaseEdgeMatcher
        """
        raise NotImplementedError

    @abstractmethod
    def get_edge(self, edge_id: str, raise_exception: bool = True) -> Optional[Edge]:
        """获取边

        :param src_id: 起点ID
        :type src_id: str
        :param dst_id: 终点ID
        :type dst_id: str
        :param edge_type_name: 边类型名称
        :type edge_type_name: str
        :return: 边
        :rtype: Edge
        """
        raise NotImplementedError


class BaseEdgeMixin(ABC):

    @abstractmethod
    def insert_edge(self, edge: Edge) -> Edge:
        """插入边

        :param edge: 边
        :type edge: Edge
        """
        raise NotImplementedError

    @abstractmethod
    def insert_edges(self, edges: List[Edge]) -> None:
        """插入多条边

        :param edges: 边集
        :type edges: List[Edge]
        """
        raise NotImplementedError

    @abstractmethod
    def delete_edge(self, edge_id: str) -> None:
        """删除边

        :param src_id: 起点ID
        :type src_id: str
        :param dst_id: 终点ID
        :type dst_id: str
        :param edge_type_name: 边类型名称
        :type edge_type_name: str
        """
        raise NotImplementedError

    @abstractmethod
    def update_edge(self, edge_id: str, props: Dict[str, Any]) -> Edge:
        """更新边

        :param src_id: 起点ID
        :type src_id: str
        :param dst_id: 终点ID
        :type dst_id: str
        :param edge_type_name: 边类型名称
        :type edge_type_name: str
        :param props: 属性
        :type props: Dict[str, Any]
        """
        raise NotImplementedError
