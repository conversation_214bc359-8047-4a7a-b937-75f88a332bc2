from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from yunfu.db.graph.graph.matchers.base_matcher import BaseNodeMatcher
from yunfu.db.graph.models.node import Node


class BaseNodeReadonlyMixin(ABC):

    @property
    @abstractmethod
    def nodes(self) -> BaseNodeMatcher:
        """获取node matcher

        :return: node matcher
        :rtype: BaseNodeMatcher
        """
        raise NotImplementedError

    @abstractmethod
    def get_node(self, node_id: str, raise_exception: bool = True) -> Optional[Node]:
        """获取节点

        :param node_id: 节点ID
        :type node_id: str
        :return: 节点
        :rtype: Node
        """
        raise NotImplementedError


class BaseNodeMixin(ABC):

    @abstractmethod
    def insert_node(self, node: Node) -> Node:
        """插入节点

        :param node: 节点
        :type node: Node
        """
        raise NotImplementedError

    @abstractmethod
    def insert_nodes(self, nodes: List[Node]) -> None:
        """插入多个点

        :param nodes: 节点集
        :type nodes: List[Node]
        """
        raise NotImplementedError

    @abstractmethod
    def delete_node(self, node_id: str, with_edge: bool = True) -> None:
        """删除节点

        :param node_id: 节点ID
        :type node_id: str
        """
        raise NotImplementedError

    @abstractmethod
    def delete_nodes(self, node_ids: List[str], with_edge: bool = True) -> None:
        """删除多个节点

        :param node_ids: 节点ID
        :type node_ids: List[str]
        """
        raise NotImplementedError

    @abstractmethod
    def update_node(self, node_id: str, props: Dict[str, Any]) -> Node:
        """更新节点

        :param node: 节点
        :type node: Node
        """
        raise NotImplementedError

    @abstractmethod
    def upsert_nodes(self, nodes: List[Node], primary_key: str) -> None:
        """更新或插入节点"""
        raise NotImplementedError
