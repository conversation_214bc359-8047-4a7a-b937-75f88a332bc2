from abc import ABC, abstractmethod
from typing import List

from yunfu.db.graph.models.edge import EdgeType


class BaseEdgeTypeReadonlyMixin(ABC):
    @property
    @abstractmethod
    def edge_types(self) -> List[str]:
        """获取所有边类型

        :return: 全部边类型
        :rtype: List[dict]
        """
        raise NotImplementedError

    @abstractmethod
    def get_edge_type(self, edge_type_name: str) -> EdgeType:
        """获取边类型

        :param edge_type_name: 边类型名称
        :type edge_type_name: str
        :return: 边类型
        :rtype: EdgeType
        """
        raise NotImplementedError

    @abstractmethod
    def has_edge_type(self, edge_type_name: str) -> bool:
        """判断边类型是否存在

        :param edge_type_name: 边类型名称
        :type edge_type_name: str
        :return: 是否存在
        :rtype: bool
        """
        raise NotImplementedError


class BaseEdgeTypeMixin(ABC):

    @abstractmethod
    def create_edge_type(self, edge_type: EdgeType, wait: bool = False) -> None:
        """创建边类型

        :param edge_type: 边类型
        :type edge_type: EdgeType
        :param wait: 是否等待, defaults to False
        :type wait: bool, optional
        """
        raise NotImplementedError

    @abstractmethod
    def create_edge_types(self, edge_types: List[EdgeType], wait: bool = False) -> None:
        """批量创建边类型

        :param edge_types: 边类型列表
        :type edge_types: List[EdgeType]
        :param wait: 是否等待, defaults to False
        :type wait: bool, optional
        """
        raise NotImplementedError

    @abstractmethod
    def alter_edge_type(self, edge_type: EdgeType, wait: bool = False) -> None:
        """修改边类型

        :param edge_type: 边类型
        :type edge_type: EdgeType
        :param wait: 是否等待, defaults to False
        :type wait: bool, optional
        """
        raise NotImplementedError

    @abstractmethod
    def drop_edge_type(self, edge_type_name: str) -> None:
        """删除边类型

        :param edge_type_name: 边类型名称
        :type edge_type_name: str
        """
        raise NotImplementedError
