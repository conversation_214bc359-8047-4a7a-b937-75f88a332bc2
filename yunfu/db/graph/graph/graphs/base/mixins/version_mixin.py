from abc import ABC, abstractmethod
from typing import List


class BaseVersionMixin(ABC):

    @abstractmethod
    def get_versions(self) -> List[str]:
        """获取所有历史版本

        :return: 历史版本列表
        :rtype: List[str]
        """
        raise NotImplementedError

    @abstractmethod
    def release(self, version: str) -> None:
        """发布版本

        :param version: 版本名
        :type version: str
        """
        raise NotImplementedError
