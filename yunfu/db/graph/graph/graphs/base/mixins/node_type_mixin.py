from abc import ABC, abstractmethod
from typing import List

from yunfu.db.graph.models.node import NodeType


class BaseNodeTypeReadonlyMixin(ABC):

    @property
    @abstractmethod
    def node_types(self) -> List[str]:
        """获取所有节点类型

        :return: 全部节点类型
        :rtype: List[dict]
        """
        raise NotImplementedError

    @abstractmethod
    def get_node_type(self, node_type_name: str) -> NodeType:
        """获取节点类型（Neo4j中的label，nebula中的tag）

        :param node_type_name: 节点类型名称
        :type node_type_name: str
        :return: 节点类型
        :rtype: NodeType
        """
        raise NotImplementedError

    @abstractmethod
    def has_node_type(self, node_type_name: str) -> bool:
        """判断节点类型是否存在

        :param node_type_name: 节点类型名称
        :type node_type_name: str
        :return: 是否存在
        :rtype: bool
        """
        raise NotImplementedError


class BaseNodeTypeMixin(ABC):

    @abstractmethod
    def create_node_type(self, node_type: NodeType, wait: bool = False) -> None:
        """创建节点类型

        :param node_type: 节点类型
        :type node_type: NodeType
        :param wait: 是否等待, defaults to False
        :type wait: bool, optional
        """
        raise NotImplementedError

    @abstractmethod
    def create_node_types(self, node_types: List[NodeType], wait: bool = False) -> None:
        """创建节点类型

        :param node_type: 节点类型列表
        :type node_type: List[NodeType]
        :param wait: 是否等待, defaults to False
        :type wait: bool, optional
        """
        raise NotImplementedError

    @abstractmethod
    def alter_node_type(self, node_type: NodeType, wait: bool = False) -> None:
        """修改节点类型

        :param node_type: 节点类型
        :type node_type: NodeType
        :param wait: 是否等待, defaults to False
        :type wait: bool, optional
        """
        raise NotImplementedError

    @abstractmethod
    def drop_node_type(self, node_type_name: str) -> None:
        """删除节点类型

        :param node_type_name: 节点类型名称
        :type node_type_name: str
        """
        raise NotImplementedError
