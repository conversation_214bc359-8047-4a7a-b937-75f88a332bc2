from typing import List

from yunfu.db.graph.clients.nebula_client import NebulaClient
from yunfu.db.graph.core.constants import DEFAULT_SPACE, NebulaMessages
from yunfu.db.graph.core.logging import get_logger
from yunfu.db.graph.graph.data_wrappers.nebula_data_wrapper import NebulaDataWrapper
from yunfu.db.graph.graph.graphs.nebula import NebulaGraph
from yunfu.db.graph.graph.templates.ngql.ngql_template import NGQLTemplate
from yunfu.db.graph.models import Index, IndexTypes, NebulaSpace, Space

from .base_graph_db import BaseGraphDb

logger = get_logger(__name__)


class NebulaGraphDb(BaseGraphDb):
    """Nebula图数据库

    1. 所有数据放在一个Nebula Space，采用Tag实现图谱的逻辑隔离
    """

    client: NebulaClient
    client_class = NebulaClient
    templates = NGQLTemplate
    data_wrapper = NebulaDataWrapper
    _space = NebulaSpace(name=DEFAULT_SPACE)

    def __post_init__(self) -> None:
        self.client.run(self.templates.use_nebula_space(self._space.name))

    def get_graph(self, name: str) -> NebulaGraph:
        """获取图谱"""
        return NebulaGraph(self.get_space(name), self.client, self.config)

    def create_space(self, space: Space, wait: bool = True) -> None:
        """创建图空间"""
        self.client.run(self.templates.create_space(space))
        index = Index(
            name=space.name, related_to=space.name, type=IndexTypes.NEBULA_TAG
        )
        self.client.run(self.templates.create_index(index))
        if wait:
            self.client.wait()

    def get_space(self, name: str) -> Space:
        """获取图空间"""
        node_type = self.data_wrapper.as_node_type(
            name,
            self.client.run(self.templates.get_space(name)).data,
        )
        return Space(name=node_type.name)

    def get_spaces(self) -> List[str]:
        """获取全部图空间"""
        space_names = self.data_wrapper.as_spaces(
            self.client.run(self.templates.get_spaces()).data
        )
        return list(filter(lambda x: Space.check_name(x), space_names))

    def drop_space(self, name: str) -> None:
        """删除图空间"""
        self.client.run(self.templates.delete_nodes_by_type(name))
        self.client.run(self.templates.drop_index(name, IndexTypes.NEBULA_TAG))
        self.client.run(self.templates.drop_space(name))

    def has_space(self, name: str) -> bool:
        """判断图空间是否存在"""
        result = self.client.run(self.templates.get_space(name), raise_exception=False)
        if NebulaMessages.TAG_NOT_EXISTED in result.message:
            return False
        return result.is_succeeded is True
