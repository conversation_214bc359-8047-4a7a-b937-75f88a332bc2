from typing import List

from yunfu.db.graph.clients.neo4j_client import Neo4jClient
from yunfu.db.graph.core.logging import get_logger
from yunfu.db.graph.graph.data_wrappers.py2neo_data_wrapper import Py2neoDataWrapper
from yunfu.db.graph.graph.graphs.neo4j import Neo4jGraph
from yunfu.db.graph.graph.templates.cypher.cypher_template import CypherTemplate
from yunfu.db.graph.models.graph import Space

from .base_graph_db import BaseGraphDb

logger = get_logger(__name__)


class Neo4jGraphDb(BaseGraphDb):
    """Neo4j图数据库

    空间延用之前的规则采用label来表示
    """

    client: Neo4jClient
    client_class = Neo4jClient
    template = CypherTemplate
    data_wrapper = Py2neoDataWrapper

    def get_graph(self, name: str) -> Neo4jGraph:
        """获取图谱"""
        return Neo4jGraph(self.get_space(name), self.client, self.config)

    def create_space(self, space: Space, wait: bool = False) -> None:
        """创建图空间"""
        raise NotImplementedError

    def get_space(self, name: str) -> Space:
        """获取图空间"""
        return Space(name=name)

    def get_spaces(self) -> List[str]:
        """获取全部图空间"""
        return self.data_wrapper.as_spaces(
            self.client.run(self.template.get_spaces()).data
        )

    def drop_space(self, name: str) -> None:
        """删除图空间"""
        self.client.run(self.template.drop_space(name))

    def has_space(self, name: str) -> bool:
        """判断图空间是否存在"""
        result = self.client.run(self.template.get_space(name), raise_exception=False)
        return bool(result.data.preview())
