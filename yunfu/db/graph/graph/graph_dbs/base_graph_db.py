from abc import ABC, abstractmethod
from typing import List, Type

from yunfu.db.graph.clients.base_client import BaseClient
from yunfu.db.graph.core.models.config import Config
from yunfu.db.graph.graph.graphs.base import BaseGraph
from yunfu.db.graph.graph.templates import Template
from yunfu.db.graph.models.graph import Space


class BaseGraphDb(ABC):

    client_class: Type[BaseClient]  # db客户端类
    template: Template  # Cypher模板类

    def __init__(self, config: Config) -> None:
        self.config = config
        self.client = self.client_class(config)
        self.__post_init__()

    def __post_init__(self) -> None:
        """初始化"""
        pass

    @abstractmethod
    def get_graph(self, name: str) -> BaseGraph:
        """获取图谱
        :param name: 图空间名称
        :type name: str
        :return: 图谱
        :rtype: BaseGraph
        """
        raise NotImplementedError

    @abstractmethod
    def create_space(self, space: Space, wait: bool = False) -> None:
        """创建图空间
        :param name: 图空间
        :type name: Space
        :param wait: 是否等待创建完成(nebula 创建图空间是异步的)
        :type wait: bool
        """
        raise NotImplementedError

    @abstractmethod
    def get_space(self, name: str) -> Space:
        """获取图空间

        :param name: 图空间名称
        :type name: Space
        :return: 图空间
        :rtype: Space
        """
        raise NotImplementedError

    @abstractmethod
    def get_spaces(self) -> List[str]:
        """获取全部图空间

        :return: 全部图空间名称
        :rtype: List[str]
        """
        raise NotImplementedError

    @abstractmethod
    def drop_space(self, name: str) -> None:
        """删除图空间
        :param name: 图空间名称
        :type name: Space
        """
        raise NotImplementedError

    @abstractmethod
    def has_space(self, name: str) -> bool:
        """判断图空间是否存在

        :param name: 图空间名称
        :type name: str
        :return: 是否存在
        :rtype: bool
        """
        raise NotImplementedError
