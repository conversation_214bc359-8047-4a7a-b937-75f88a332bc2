from typing import Any, List, Optional, Sequence, Tuple


class CypherMatchTemplate:

    @classmethod
    def match_node(
        cls,
        space: str,
        version: str,
        types: frozenset = frozenset(),
        props: Optional[List[Tuple[str, str, Any]]] = None,
        order_by: Optional[Sequence[str]] = None,
        skip: Optional[int] = None,
        limit: Optional[int] = None,
        count: bool = False,
        where_query: Optional[str] = None,
        output: Optional[str] = None,
    ) -> str:
        clauses = [
            f"MATCH (n:{space}:{version}{':' if types else ''}{':'.join(types)})"
        ]
        if where_query:
            clauses.append(where_query)
        elif props:
            for index, (prop, symbol, value) in enumerate(props):
                if index == 0:
                    clauses.append(f"WHERE n.{prop} {symbol} {cls._format_prop(value)}")
                else:
                    clauses.append(f"AND n.{prop} {symbol} {cls._format_prop(value)}")
        if count:
            clauses.append("RETURN count(n)")
        else:
            if output:
                clauses.append(f"RETURN {output}")
            else:
                clauses.append("RETURN n")
            if order_by:
                clauses.append(f"ORDER BY {', '.join(order_by)}")
            if skip is not None:
                clauses.append(f"SKIP {skip}")
            if limit is not None:
                clauses.append(f"LIMIT {limit}")
        return " ".join(clauses)

    @classmethod
    def match_edge(
        cls,
        space: str,
        version: str,
        edge_types: frozenset = frozenset(),
        src_node_types: frozenset = frozenset(),
        dst_node_types: frozenset = frozenset(),
        props: Optional[List[Tuple[str, str, Any]]] = None,
        src_node_props: Optional[List[Tuple[str, str, Any]]] = None,
        dst_node_props: Optional[List[Tuple[str, str, Any]]] = None,
        order_by: Optional[Sequence[str]] = None,
        skip: Optional[int] = None,
        limit: Optional[int] = None,
        count: bool = False,
        where_query: Optional[str] = None,
        output: Optional[str] = None,
    ) -> str:
        clauses = [
            f"MATCH (src:{space}:{version}{':' if src_node_types else ''}{':'.join(src_node_types)})"
            f"-[r{':' if edge_types else ''}{'|'.join(edge_types)}]"
            f"->(dst:{space}:{version}{':' if dst_node_types else ''}{':'.join(dst_node_types)})",
        ]
        prop_clauses = [f"WHERE r.{version} = true"]
        if where_query:
            prop_clauses.append(where_query)
        else:
            if props:
                for prop, symbol, value in props:
                    prop_clauses.append(f"r.{prop} {symbol} {cls._format_prop(value)}")
            if src_node_props:
                for prop, symbol, value in src_node_props:
                    prop_clauses.append(
                        f"src.{prop} {symbol} {cls._format_prop(value)}"
                    )
            if dst_node_props:
                for prop, symbol, value in dst_node_props:
                    prop_clauses.append(
                        f"dst.{prop} {symbol} {cls._format_prop(value)}"
                    )
        clauses.append(" AND ".join(prop_clauses))
        if count:
            clauses.append("RETURN count(r)")
        else:
            if output:
                clauses.append(f"RETURN {output}")
            else:
                clauses.append("RETURN r, src, dst")
            if order_by:
                clauses.append(f"ORDER BY {', '.join(order_by)}")
            if skip:
                clauses.append(f"SKIP {skip}")
            if limit:
                clauses.append(f"LIMIT {limit}")
        return " ".join(clauses)

    @staticmethod
    def _format_prop(prop_value: Any) -> str:
        """格式化属性值"""
        if isinstance(prop_value, str):
            return f'"{prop_value}"'
        return repr(prop_value)
