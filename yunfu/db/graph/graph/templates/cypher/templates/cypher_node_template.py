from typing import Any, Dict, List

from yunfu.db.graph.models import Node

from .utils import CypherTemplateUtils as Utils


class CypherNodeTemplate:

    @staticmethod
    def get_node(space: str, node_id: str, version: str) -> str:
        """获取点"""
        return f"MATCH (n:{space}:{version}) WHERE n._id = {Utils.format_prop(node_id)} RETURN n"

    @classmethod
    def insert_node(
        cls,
        space: str,
        node: Node,
        version: str,
    ) -> str:
        return cls.insert_nodes(space, [node], version)

    @staticmethod
    def insert_nodes(space: str, nodes: List[Node], version: str) -> str:
        if not nodes:
            raise ValueError("Nodes cannot be empty")
        clauses = []
        for index, node in enumerate(nodes):
            props = node.props
            props.update({"_id": node.id})
            clauses.append(
                f"(n{index}:{space}:{version}{':' if node.types else ''}"
                f"{':'.join(node.types)} {Utils.format_props(props)})"
            )
        return f"CREATE {', '.join(clauses)}"

    @classmethod
    def delete_node(cls, space: str, node_id: str, with_edge: bool = True) -> str:
        return cls.delete_nodes(space, [node_id], with_edge)

    @staticmethod
    def delete_nodes(space: str, node_ids: List[str], with_edge: bool = True) -> str:
        if not node_ids:
            raise ValueError("Node IDs cannot be empty")
        return (
            f"MATCH (n:{space}) WHERE n._id IN"
            f" [{', '.join(Utils.format_prop(node_id) for node_id in node_ids)}]"
            f" {'DETACH ' if with_edge else ''}DELETE n"
        )

    @staticmethod
    def update_node(
        space: str, node_id: str, props: Dict[str, Any], **kwargs: Any
    ) -> str:
        return (
            f"MATCH (n:{space}) WHERE n._id = {Utils.format_prop(node_id)} SET"
            f" {', '.join([f'n.{k} = {Utils.format_prop(v)}' for k, v in props.items()])}"
        )
