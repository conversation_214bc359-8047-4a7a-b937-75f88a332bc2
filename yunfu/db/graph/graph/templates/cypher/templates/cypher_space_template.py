from yunfu.db.graph.core.constants import SPACE_PREFIX


class CypherSpaceTemplate:

    @staticmethod
    def get_spaces() -> str:
        """显示所有图空间"""
        return f"CALL db.labels() YIELD label WHERE label STARTS WITH '{SPACE_PREFIX}' RETURN label"

    @staticmethod
    def get_space(name: str) -> str:
        """获取图空间信息"""
        return f"CALL db.labels() YIELD label WHERE label = '{name}' RETURN label"

    @staticmethod
    def drop_space(name: str) -> str:
        """删除图空间"""
        return f"MATCH (n:{name}) DETACH DELETE n"
