from typing import Any, Dict


class CypherTemplateUtils:

    @classmethod
    def format_props(cls, props: Dict[str, Any]) -> str:
        """格式化属性"""
        return f"{{ {', '.join([f'{k}: {cls.format_prop(v)}' for k, v in props.items()])} }}"

    @staticmethod
    def format_prop(prop_value: Any) -> str:
        """格式化属性值"""
        if isinstance(prop_value, str):
            return f'"{prop_value}"'
        if isinstance(prop_value, bool):
            return "true" if prop_value else "false"
        if prop_value is None:
            return "NULL"
        return repr(prop_value)
