from typing import Any, Dict

from yunfu.db.graph.models import Edge

from .utils import CypherTemplateUtils as Utils


class CypherEdgeTemplate:

    @staticmethod
    def get_edge(space_name: str, edge_id: str, version: str) -> str:
        return (
            f"MATCH (src:{space_name})-[r]->(dst:{space_name})"
            f" WHERE r._id = {Utils.format_prop(edge_id)}"
            f" AND r.{version} = true"
            f" RETURN r,src,dst"
        )

    @staticmethod
    def insert_edge(space_name: str, edge: Edge, version: str) -> str:
        edge.props[version] = True
        edge.props["_id"] = edge.id
        return (
            f"MATCH (src:{space_name} {{_id: {Utils.format_prop(edge.src_id)}}}),"
            f" (dst:{space_name} {{_id: {Utils.format_prop(edge.dst_id)}}})"
            f" CREATE (src)-[r:{edge.type} {Utils.format_props(edge.props)}]->(dst)"
        )

    @staticmethod
    def update_edge(space_name: str, edge_id: str, props: Dict[str, Any]) -> str:
        return (
            f"MATCH (src:{space_name})-[r]->(dst:{space_name})"
            f" WHERE r._id = {Utils.format_prop(edge_id)}"
            f" SET {', '.join([f'r.{k} = {Utils.format_prop(v)}' for k, v in props.items()])}"
        )

    @staticmethod
    def delete_edge(space_name: str, edge_id: str) -> str:
        return (
            f"MATCH (src:{space_name})-[r]->(dst:{space_name})"
            f" WHERE r._id = {Utils.format_prop(edge_id)}"
            f" DELETE r"
        )
