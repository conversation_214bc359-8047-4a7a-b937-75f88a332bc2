from typing import Any, Dict, List

from yunfu.db.graph.models import Edge

from .utils import NGQLTemplateUtils as Utils


class NGQLEdgeTemplate:

    @classmethod
    def get_edge(cls, space: str, edge_id: str, version: str) -> str:
        """获取边"""
        return (
            f"MATCH (src:{space}:{version})-[e]->(dst:{space}:{version}) "
            f" WITH src, dst, e, properties(e) as props"
            f" WHERE props.id == {Utils.format_prop(edge_id)}"
            f" AND props.{version} == true"
            f" RETURN e, src, dst LIMIT 1;"
        )

    @classmethod
    def insert_edge(
        cls,
        edge: Edge,
        version: str,
        type: str,
        props: List[str],
        if_not_exists: bool = True,
    ) -> str:
        """插入边"""
        return cls.insert_edges([edge], version, type, props, if_not_exists)

    @staticmethod
    def insert_edges(
        edges: List[Edge],
        version: str,
        type: str,
        props: List[str],
        if_not_exists: bool = True,
    ) -> str:
        """插入多条边

        语法: INSERT EDGE [IF NOT EXISTS] <edge_type> ( <prop_name_list> ) VALUES
            <src_vid> -> <dst_vid>[@<rank>] : ( <prop_value_list> )
            [, <src_vid> -> <dst_vid>[@<rank>] : ( <prop_value_list> ), ...];

            <prop_name_list> ::=
            [ <prop_name> [, <prop_name> ] ...]

            <prop_value_list> ::=
            [ <prop_value> [, <prop_value> ] ...]
        """
        if not edges:
            raise ValueError("Edges cannot be empty")
        tag_props_cypher = f"`{type}` ({', '.join([f'`{_}`' for _ in props])})"
        edges_cypher_list = []
        for edge in edges:
            edge.props[version] = True
            edge.props["id"] = edge.id
            if edge.type != type:
                raise ValueError(f"Edge type mismatch: {edge.type} != {type}")
            prop_values = [Utils.format_prop(edge.props.get(p)) for p in props]
            edges_cypher_list.append(
                f"{Utils.get_edge_snippet_by_edge(edge)}: ({', '.join(prop_values)})"
            )
        nodes_cypher = ", ".join(edges_cypher_list)
        return (
            f"INSERT EDGE {'IF NOT EXISTS ' if if_not_exists else ''}"
            f"{tag_props_cypher} VALUES {nodes_cypher};"
        )

    @staticmethod
    def update_edge(edge: Edge, props: Dict[str, Any], **kwargs: Any) -> str:
        """更新边

        语法: UPDATE EDGE ON <edge_type>
            <src_vid> -> <dst_vid> [@<rank>]
            SET <update_prop>
            [WHEN <condition>]
            [YIELD <output>]
        """
        condition = kwargs.get("condition")
        output = kwargs.get("output")
        return (
            f"UPDATE EDGE ON `{edge.type}` {Utils.get_edge_snippet_by_edge(edge)} SET "
            f"{', '.join(f'`{k}` = {Utils.format_prop(v)}' for k, v in props.items())};"
            f"{f' WHEN {condition}' if condition else ''}"
            f"{f' YIELD {output}' if output else ''}"
        )

    @staticmethod
    def delete_edge(edge: Edge) -> str:
        """删除边"""
        return f"DELETE EDGE `{edge.type}` {Utils.get_edge_snippet_by_edge(edge)};"
