from yunfu.db.graph.models import NebulaSpace


class NGQLNebulaSpaceTemplate:

    @staticmethod
    def get_nebula_spaces() -> str:
        """显示所有 Nebula 图空间"""
        return "SHOW SPACES;"

    @staticmethod
    def get_nebula_space(name: str) -> str:
        """获取 Nebula 图空间信息

        语法: DESC[RIBE] SPACE <graph_space_name>;
        """
        return f"DESCRIBE SPACE {name};"

    @staticmethod
    def create_nebula_space(space: NebulaSpace, if_not_exists: bool = False) -> str:
        """创建 Nebula 图空间

        语法: CREATE SPACE [IF NOT EXISTS] <graph_space_name> (
                [partition_num = <partition_number>,]
                [replica_factor = <replica_number>,]
                node_id_type = {FIXED_STRING(<N>) | INT[64]}
                )
                [COMMENT = '<comment>'];
        """
        return (
            f"CREATE SPACE {'IF NOT EXISTS ' if if_not_exists else ''}{space.name} "
            f"({f'partition_num = {space.partition_num},' if space.partition_num else ''} "
            f"{f'replica_factor = {space.replica_factor},' if space.replica_factor else ''} "
            f"vid_type = {space.node_id_type})"
            f"{f' COMMENT = {repr(space.comment)}' if space.comment else ''};"
        )

    @staticmethod
    def clear_nebula_space(name: str, if_exists: bool = False) -> str:
        """清空 Nebula 图空间中的点和边(但不会删除图空间本身以及其中的 Schema 信息)

        语法: CLEAR SPACE [IF EXISTS] <graph_space_name>;
        """
        return f"CLEAR SPACE {'IF EXISTS ' if if_exists else ''}{name};"

    @staticmethod
    def drop_nebula_space(name: str, if_exists: bool = False) -> str:
        """删除 Nebula 指定图空间以及其中的所有信息

        语法: DROP SPACE [IF EXISTS] <graph_space_name>;
        """
        return f"DROP SPACE {'IF EXISTS ' if if_exists else ''}{name};"

    @staticmethod
    def use_nebula_space(name: str) -> str:
        """指定 Nebula 图空间

        语法: USE <graph_space_name>;
        """
        return f"USE {name};"
