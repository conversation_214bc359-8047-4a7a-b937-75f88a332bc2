from typing import Any, List, Optional, Sequence, Tuple

from .utils import NGQLTemplateUtils as Utils


class NGQLMatchTemplate:

    @staticmethod
    def match_node(
        space: str,
        version: str,
        types: frozenset = frozenset(),
        props: Optional[List[Tuple[str, str, Any]]] = None,
        order_by: Optional[Sequence[str]] = None,
        skip: Optional[int] = None,
        limit: Optional[int] = None,
        count: bool = False,
        where_query: Optional[str] = None,
        output: Optional[str] = None,
    ) -> str:
        clauses = [
            f"MATCH (v:{space}:{version}{':' if types else ''}{':'.join(types or [])})",
        ]
        if where_query:
            clauses.append(where_query)
        elif props:
            for index, (prop, symbol, value) in enumerate(props):
                symbol = "==" if symbol == "=" else symbol
                if index == 0:
                    clauses.append(
                        f"WHERE v.{space}.{prop} {symbol} {Utils.format_prop(value)}"
                    )
                else:
                    clauses.append(
                        f"AND v.{space}.{prop} {symbol} {Utils.format_prop(value)}"
                    )
        if count:
            clauses.append("RETURN count(v)")
        else:
            if output:
                clauses.append(f"RETURN {output}")
            else:
                clauses.append("RETURN v")
            if order_by:
                clauses.append(f"ORDER BY {', '.join(order_by)}")
            if skip:
                clauses.append(f"SKIP {skip}")
            if limit:
                clauses.append(f"LIMIT {limit}")
        return " ".join(clauses)

    @staticmethod
    def match_edge(
        space: str,
        version: str,
        edge_types: frozenset = frozenset(),
        src_node_types: frozenset = frozenset(),
        dst_node_types: frozenset = frozenset(),
        props: Optional[List[Tuple[str, str, Any]]] = None,
        src_node_props: Optional[List[Tuple[str, str, Any]]] = None,
        dst_node_props: Optional[List[Tuple[str, str, Any]]] = None,
        order_by: Optional[Sequence[str]] = None,
        skip: Optional[int] = None,
        limit: Optional[int] = None,
        count: bool = False,
        where_query: Optional[str] = None,
        output: Optional[str] = None,
    ) -> str:
        clauses = [
            f"MATCH (src:{space}:{version}{':' if src_node_types else ''}{':'.join(src_node_types)})"
            f"-[e{':' if edge_types else ''}{'|'.join(edge_types)}]"
            f"->(dst:{space}:{version}{':' if dst_node_types else ''}{':'.join(dst_node_types)})",
        ]
        clauses.append("WITH src, dst, e, properties(e) as props")
        prop_clauses = [f"WHERE props.{version} == true"]
        if where_query:
            prop_clauses.append(where_query.replace("WHERE", ""))
        else:
            if props:
                for prop, symbol, value in props:
                    symbol = "==" if symbol == "=" else symbol
                    prop_clauses.append(
                        f"props.{prop} {symbol} {Utils.format_prop(value)}"
                    )
            if src_node_props:
                for prop, symbol, value in src_node_props:
                    symbol = "==" if symbol == "=" else symbol
                    prop_clauses.append(
                        f"src.{space}.{prop} {symbol} {Utils.format_prop(value)}"
                    )
            if dst_node_props:
                for prop, symbol, value in dst_node_props:
                    symbol = "==" if symbol == "=" else symbol
                    prop_clauses.append(
                        f"dst.{space}.{prop} {symbol} {Utils.format_prop(value)}"
                    )
        clauses.append(" AND ".join(prop_clauses))
        if count:
            clauses.append("RETURN count(e)")
        else:
            if output:
                clauses.append(f"RETURN {output}")
            else:
                clauses.append("RETURN e, src, dst")
            if order_by:
                clauses.append(f"ORDER BY {', '.join(order_by)}")
            if skip:
                clauses.append(f"SKIP {skip}")
            if limit:
                clauses.append(f"LIMIT {limit}")
        return " ".join(clauses)
