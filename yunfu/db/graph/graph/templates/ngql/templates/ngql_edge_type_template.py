from yunfu.db.graph.models import EdgeType

from .utils import NGQLTemplateUtils as Utils


class NGQLEdgeTypeTemplate:

    @staticmethod
    def get_edge_types(space_name: str) -> str:
        """显示当前图空间内的所有 Edge type 名称

        语法: SHOW EDGES;
        """
        return "SHOW EDGES;"

    @staticmethod
    def get_edge_type(space_name: str, edge_type_name: str) -> str:
        """显示指定 Edge type 的详细信息

        语法: DESC[RIBE] EDGE <edge_type_name>;
        """
        return f"DESCRIBE EDGE `{edge_type_name}`;"

    @staticmethod
    def create_edge_type(
        space_name: str, edge_type: EdgeType, if_not_exists: bool = False
    ) -> str:
        """创建 Edge type

        语法: CREATE EDGE [IF NOT EXISTS] <edge_type_name>
                (
                <prop_name> <data_type> [NULL | NOT NULL] [DEFAULT <default_value>] [COMMENT '<comment>']
                [{, <prop_name> <data_type> [NULL | NOT NULL] [DEFAULT <default_value>] [COMMENT '<comment>']} ...]
                )
                [TTL_DURATION = <ttl_duration>]
                [TTL_COL = <prop_name>]
                [COMMENT = '<comment>'];
        """
        property_ngql = ", ".join(
            [
                f"`{p.name}` {p.type} {'NULL' if p.nullable else 'NOT NULL'}"
                f"{f' DEFAULT {Utils.format_prop(p.default)}' if p.default is not None else ''}"
                f"{f' COMMENT {repr(p.comment)}' if p.comment else ''}"
                for p in edge_type.props
            ]
        )
        return (
            f"CREATE EDGE {' IF NOT EXISTS' if if_not_exists else ''}`{edge_type.name}`"
            f" ({property_ngql})"
            f"{f' TTL_DURATION = {edge_type.ttl_duration}, TTL_COL = {edge_type.ttl_col}' if edge_type.ttl_col else ''}"
            f"{f' COMMENT = {repr(edge_type.comment)}' if edge_type.comment else ''};"
        )

    @staticmethod
    def alter_edge_type(
        space_name: str, edge_type: EdgeType, new_edge_type: EdgeType
    ) -> str:
        """修改 Edge type 结构

        语法: ALTER EDGE <edge_type_name>
                <alter_definition> [[, alter_definition] ...]
                [ttl_definition [, ttl_definition] ... ]
                [COMMENT = '<comment>'];

            alter_definition:
            | ADD    (prop_name data_type [NULL | NOT NULL] [DEFAULT <default_value>] [COMMENT '<comment>'])
            | DROP   (prop_name)
            | CHANGE (prop_name data_type [NULL | NOT NULL] [DEFAULT <default_value>] [COMMENT '<comment>'])

            ttl_definition:
                TTL_DURATION = ttl_duration, TTL_COL = prop_name
        """
        if edge_type.name != new_edge_type.name:
            raise ValueError("Edge name cannot be changed")
        name2edge_props = {}
        edge_props_name = set()
        for p in edge_type.props:
            name2edge_props[p.name] = p
            edge_props_name.add(p.name)
        new_edge_props_name = {p.name for p in new_edge_type.props}
        drop_props = ", ".join(
            p.name for p in edge_type.props if p.name not in new_edge_props_name
        )
        add_props = ", ".join(
            f"`{p.name}` {p.type} {'NULL' if p.nullable else 'NOT NULL'}"
            f"{f' DEFAULT {Utils.format_prop(p.default)}' if p.default is not None else ''}"
            f"{f' COMMENT {repr(p.comment)}' if p.comment else ''}"
            for p in new_edge_type.props
            if p.name not in edge_props_name
        )
        change_props = ", ".join(
            f"`{p.name}` {p.type} {'NULL' if p.nullable else 'NOT NULL'}"
            f"{f' DEFAULT {Utils.format_prop(p.default)}' if p.default is not None else ''}"
            f"{f' COMMENT {repr(p.comment)}' if p.comment else ''}"
            for p in new_edge_type.props
            if p.name in edge_props_name and p != name2edge_props[p.name]
        )
        return (
            f"ALTER EDGE `{edge_type.name}`"
            f"{f' DROP ({drop_props})' if drop_props else ''}"
            f"{',' if drop_props and (add_props or change_props) else ''}"
            f"{f' ADD ({add_props})' if add_props else ''}"
            f"{',' if add_props and change_props else ''}"
            f"{f' CHANGE ({change_props})' if change_props else ''}"
            f"{f' TTL_DURATION = {new_edge_type.ttl_duration}, TTL_COL = {new_edge_type.ttl_col}' if new_edge_type.ttl_duration else ''}"  # noqa
            f"{f' COMMENT = {repr(new_edge_type.comment)}' if new_edge_type.comment else ''};"
        )

    @staticmethod
    def drop_edge_type(
        space_name: str, edge_type_name: str, if_exists: bool = False
    ) -> str:
        """删除当前工作空间内所有边上的指定 Edge type

        语法: DROP EDGE [IF EXISTS] <edge_type_name>;
        """
        return f"DROP EDGE {' IF EXISTS' if if_exists else ''}`{edge_type_name}`;"
