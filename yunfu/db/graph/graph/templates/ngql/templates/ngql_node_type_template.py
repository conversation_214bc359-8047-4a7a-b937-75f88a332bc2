from yunfu.db.graph.models import NodeType

from .utils import NGQLTemplateUtils as Utils


class NGQLNodeTypeTemplate:

    @staticmethod
    def get_node_types(space_name: str) -> str:
        """显示当前图空间内的所有 Tag 名称

        语法: SHOW TAGS;
        """
        return "SHOW TAGS;"

    @staticmethod
    def get_node_type(space_name: str, node_type_name: str) -> str:
        """显示指定 Tag 的详细信息

        语法: DESC[RIBE] TAG <tag_name>;
        """
        return f"DESCRIBE TAG {node_type_name};"

    @staticmethod
    def create_node_type(
        space_name: str, node_type: NodeType, if_not_exists: bool = False
    ) -> str:
        """创建 Tag

        语法: CREATE TAG [IF NOT EXISTS] <tag_name>
                (
                <prop_name> <data_type> [NULL | NOT NULL] [DEFAULT <default_value>] [COMMENT '<comment>']
                [{, <prop_name> <data_type> [NULL | NOT NULL] [DEFAULT <default_value>] [COMMENT '<comment>']} ...]
                )
                [TTL_DURATION = <ttl_duration>]
                [TTL_COL = <prop_name>]
                [COMMENT = '<comment>'];
        """
        property_ngql = ", ".join(
            [
                f"`{p.name}` {p.type} {'NULL' if p.nullable else 'NOT NULL'}"
                f"{f' DEFAULT {Utils.format_prop(p.default)}' if p.default is not None else ''}"
                f"{f' COMMENT {repr(p.comment)}' if p.comment else ''}"
                for p in node_type.props
            ]
        )
        return (
            f"CREATE TAG {'IF NOT EXISTS ' if if_not_exists else ''}{node_type.name}"
            f" ({property_ngql})"
            f"{f' TTL_DURATION = {node_type.ttl_duration}, TTL_COL = {node_type.ttl_col}' if node_type.ttl_col else ''}"
            f"{f' COMMENT = {repr(node_type.comment)}' if node_type.comment else ''}"
        )

    @staticmethod
    def alter_node_type(
        space_name: str, node_type: NodeType, new_node_type: NodeType
    ) -> str:
        """修改 Tag 结构

        语法: ALTER TAG <tag_name>
                <alter_definition> [[, alter_definition] ...]
                [ttl_definition [, ttl_definition] ... ]
                [COMMENT = '<comment>'];

            alter_definition:
            | ADD    (prop_name data_type [NULL | NOT NULL] [DEFAULT <default_value>] [COMMENT '<comment>'])
            | DROP   (prop_name)
            | CHANGE (prop_name data_type [NULL | NOT NULL] [DEFAULT <default_value>] [COMMENT '<comment>'])

            ttl_definition:
                TTL_DURATION = ttl_duration, TTL_COL = prop_name
        """
        if node_type.name != new_node_type.name:
            raise ValueError("Tag name cannot be changed")
        name2node_props = {}
        node_props_name = set()
        for p in node_type.props:
            name2node_props[p.name] = p
            node_props_name.add(p.name)
        new_node_props_name = {p.name for p in new_node_type.props}
        drop_props = ", ".join(
            p.name for p in node_type.props if p.name not in new_node_props_name
        )
        add_props = ", ".join(
            f"`{p.name}` {p.type} {'NULL' if p.nullable else 'NOT NULL'}"
            f"{f' DEFAULT {Utils.format_prop(p.default)}' if p.default is not None else ''}"
            f"{f' COMMENT {repr(p.comment)}' if p.comment else ''}"
            for p in new_node_type.props
            if p.name not in node_props_name
        )
        change_props = ", ".join(
            f"`{p.name}` {p.type} {'NULL' if p.nullable else 'NOT NULL'}"
            f"{f' DEFAULT {Utils.format_prop(p.default)}' if p.default is not None else ''}"
            f"{f' COMMENT {repr(p.comment)}' if p.comment else ''}"
            for p in new_node_type.props
            if p.name in node_props_name and p != name2node_props[p.name]
        )
        return (
            f"ALTER TAG {node_type.name}"
            f"{f' DROP ({drop_props})' if drop_props else ''}"
            f"{',' if drop_props and (add_props or change_props) else ''}"
            f"{f' ADD ({add_props})' if add_props else ''}"
            f"{',' if add_props and change_props else ''}"
            f"{f' CHANGE ({change_props})' if change_props else ''}"
            f"{f' TTL_DURATION = {new_node_type.ttl_duration}, TTL_COL = {new_node_type.ttl_col}' if new_node_type.ttl_duration else ''}"  # noqa
            f"{f' COMMENT = {repr(new_node_type.comment)}' if new_node_type.comment else ''};"
        )

    @staticmethod
    def drop_node_type(
        space_name: str, node_type_name: str, if_exists: bool = False
    ) -> str:
        """删除当前工作空间内所有点上的指定 Tag

        语法: DROP TAG [IF EXISTS] <tag_name>;
        """
        return f"DROP TAG {' IF EXISTS' if if_exists else ''}{node_type_name};"
