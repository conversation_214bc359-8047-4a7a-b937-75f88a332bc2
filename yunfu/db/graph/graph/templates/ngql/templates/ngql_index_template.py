import re
from typing import List

from yunfu.db.graph.models import Index, IndexTypes


class NGQLIndexTemplate:

    @staticmethod
    def get_indexes(index_type: IndexTypes) -> str:
        """显示所有索引

        语法: SHOW {TAG | EDGE} INDEXES;
        """
        return f"SHOW {index_type.value} INDEXES;"

    @staticmethod
    def get_index(index_name: str, index_type: IndexTypes) -> str:
        """显示指定索引的详细信息

        语法: DESC[RIBE] {TAG | EDGE} INDEX <index_name>;
        """
        return f"DESCRIBE {index_type.value} INDEX {index_name};"

    @staticmethod
    def create_index(index: Index, if_not_exists: bool = False) -> str:
        """创建索引

        语法: CREATE {TAG | EDGE} INDEX [IF NOT EXISTS] <index_name>
                ON {<tag_name> | <edge_name>} ([<prop_name_list>]) [COMMENT '<comment>'];
        """
        props = []
        if index.props:
            pattern = re.compile(r"(\w+)\((\d+)\)")
            for prop in index.props:
                prop = pattern.sub(r"`\1`(\2)", prop)
                props.append(prop)
        return (
            f"CREATE {index.type.value} INDEX {'IF NOT EXISTS ' if if_not_exists else ''}{index.name} "
            f"ON {index.related_to}"
            f"({', '.join(props)})"
            f"{f' COMMENT {index.comment}' if index.comment else ''};"
        )

    @classmethod
    def rebuild_index(cls, index_name: str, index_type: IndexTypes) -> str:
        """重建索引

        语法: REBUILD {TAG | EDGE} INDEX [<index_name_list>];
        """
        return cls.rebuild_indexes([index_name], index_type)

    @staticmethod
    def rebuild_indexes(index_names: List[str], index_type: IndexTypes) -> str:
        """重建索引

        语法: REBUILD {TAG | EDGE} INDEX [<index_name_list>];
        """
        return f"REBUILD {index_type.value} INDEX {', '.join(index_names)};"

    @staticmethod
    def get_index_status(index_type: IndexTypes) -> str:
        """显示索引状态

        语法: SHOW {TAG | EDGE} INDEX STATUS;
        """
        return f"SHOW {index_type.value} INDEX STATUS;"

    @staticmethod
    def drop_index(
        name: str,
        index_type: IndexTypes,
        if_exists: bool = False,
    ) -> str:
        """删除索引

        语法: DROP {TAG | EDGE} INDEX [IF EXISTS] <index_name>;
        """
        return (
            f""
            f"DROP {index_type.value} INDEX {'IF EXISTS' if if_exists else ''} {name};"
        )
