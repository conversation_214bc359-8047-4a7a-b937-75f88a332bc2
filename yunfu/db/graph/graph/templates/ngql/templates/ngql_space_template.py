from yunfu.db.graph.models import NodeType, Space

from .utils import NGQLTemplateUtils as Utils


class NGQLSpaceTemplate:

    @staticmethod
    def get_spaces() -> str:
        """显示所有图空间"""
        return "SHOW TAGS;"

    @staticmethod
    def get_space(name: str) -> str:
        """获取图空间信息"""
        return f"DESCRIBE TAG {name};"

    @staticmethod
    def create_space(space: Space, if_not_exists: bool = False) -> str:
        """创建图空间"""
        node_type = NodeType(name=space.name, props=space.props)
        property_ngql = ", ".join(
            [
                f"`{p.name}` {p.type} {'NULL' if p.nullable else 'NOT NULL'}"
                f"{f' DEFAULT {Utils.format_prop(p.default)}' if p.default is not None else ''}"
                f"{f' COMMENT {repr(p.comment)}' if p.comment else ''}"
                for p in node_type.props
            ]
        )
        return (
            f"CREATE TAG {'IF NOT EXISTS ' if if_not_exists else ''}{node_type.name}"
            f" ({property_ngql})"
            f"{f' TTL_DURATION = {node_type.ttl_duration}, TTL_COL = {node_type.ttl_col}' if node_type.ttl_col else ''}"
            f"{f' COMMENT = {repr(node_type.comment)}' if node_type.comment else ''}"
        )

    @staticmethod
    def drop_space(name: str, if_exists: bool = True) -> str:
        """删除图空间"""
        return f"DROP TAG {'IF EXISTS ' if if_exists else ''}{name};"
