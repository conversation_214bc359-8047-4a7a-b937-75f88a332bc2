from typing import Any

from yunfu.db.graph.models import Edge


class NGQLTemplateUtils:

    @staticmethod
    def format_prop(value: Any) -> str:
        """格式化属性值

        TODO: Nebula属性的数据类型包括数值、布尔、字符串、日期与时间以及地理空间
        """
        if isinstance(value, bool):
            return repr(value).lower()
        if value is None:
            return "NULL"
        return repr(value)

    @classmethod
    def get_edge_snippet_by_edge(cls, edge: Edge) -> str:
        return cls.get_edge_snippet(edge.src_id, edge.dst_id, edge.rank)

    @classmethod
    def get_edge_snippet(cls, src_id: str, dst_id: str, rank: int) -> str:
        """nebula 边相关语句的固定格式

        语法: <src_vid> -> <dst_vid>[@<rank>]
        """
        return (
            f"{cls.format_node_id(src_id)} -> {cls.format_node_id(dst_id)}"
            f"{f'@{rank}' if rank != 0 else ''}"
        )

    @staticmethod
    def format_node_id(node_id: str) -> str:
        """格式化节点ID"""
        return repr(node_id)
