from typing import Any, Dict, List

from yunfu.db.graph.models import Node

from .utils import NGQLTemplateUtils as Utils


class NGQLNodeTemplate:

    @staticmethod
    def get_node(space: str, node_id: str, version: str) -> str:
        """获取点"""
        return f"MATCH (v:{space}:{version}) WHERE id(v) == {Utils.format_node_id(node_id)} RETURN v LIMIT 1;"

    @classmethod
    def insert_node(
        cls,
        space: str,
        nodes: Node,
        version: str,
        if_not_exists: bool = True,
    ) -> str:
        """插入点"""
        return cls.insert_nodes(space, [nodes], version, if_not_exists)

    @staticmethod
    def insert_nodes(
        space: str,
        nodes: List[Node],
        version: str,
        if_not_exists: bool = True,
    ) -> str:
        """插入多个点

        语法: INSERT VERTEX [IF NOT EXISTS] [tag_props, [tag_props] ...]
                VALUES VID: ([prop_value_list])
            tag_props:
                tag_name ([prop_name_list])

            prop_name_list:
                [prop_name [, prop_name] ...]

            prop_value_list:
            [   prop_value [, prop_value] ...]
        """
        if not nodes:
            raise ValueError("Nodes cannot be empty")
        # 所有属性都放在space_name这个tag中
        type2props = {space: list(nodes[0].props.keys())}
        type2props.update({_: [] for _ in nodes[0].types if _ != space})
        type2props.update({version: []})
        tag_props_cypher_list = []
        prop_names = []
        for type_, props in type2props.items():
            tag_props_cypher_list.append(
                f"{type_} ({', '.join([f'`{_}`' for _ in props])})"
            )
            prop_names.extend(props)
        tag_props_cypher = ", ".join(tag_props_cypher_list)
        nodes_cypher_list = []
        for node in nodes:
            prop_values = [Utils.format_prop(node.props.get(p)) for p in prop_names]
            nodes_cypher_list.append(
                f"{Utils.format_node_id(node.id)}: ({', '.join(prop_values)})"
            )
        nodes_cypher = ", ".join(nodes_cypher_list)
        return (
            f"INSERT VERTEX {'IF NOT EXISTS ' if if_not_exists else ''}"
            f"{tag_props_cypher} VALUES {nodes_cypher};"
        )

    @classmethod
    def delete_node(cls, node_id: str, with_edge: bool = True) -> str:
        """删除点"""
        return cls.delete_nodes([node_id], with_edge)

    @staticmethod
    def delete_nodes(node_ids: List[str], with_edge: bool = True) -> str:
        """删除多个点

        语法: DELETE VERTEX <vid> [ , <vid> ... ] [WITH EDGE];
        """
        if not node_ids:
            raise ValueError("node_ids cannot be empty")
        return (
            f"DELETE VERTEX {', '.join(Utils.format_node_id(_) for _ in node_ids)} "
            f"{'WITH EDGE' if with_edge else ''};"
        )

    @staticmethod
    def delete_nodes_by_type(node_type: str) -> str:
        return f"LOOKUP ON {node_type} YIELD id(vertex) AS id | DELETE VERTEX $-.id WITH EDGE"

    @staticmethod
    def update_node(
        node_id: str,
        node_type_name: str,
        props: Dict[str, Any],
        **kwargs: Any,
    ) -> str:
        """更新点

        语法: UPDATE VERTEX ON <tag_name> <vid>
                SET <update_prop>
                [WHEN <condition>]
                [YIELD <output>]
        """
        # TODO: 如何支持SET age = age + 2 ?
        condition = kwargs.get("condition")
        output = kwargs.get("output")
        return (
            f"UPDATE VERTEX ON {node_type_name} {Utils.format_node_id(node_id)} SET "
            f"{', '.join(f'`{k}` = {Utils.format_prop(v)}' for k, v in props.items())};"
            f"{f' WHEN {condition}' if condition else ''}"
            f"{f' YIELD {output}' if output else ''}"
        )
