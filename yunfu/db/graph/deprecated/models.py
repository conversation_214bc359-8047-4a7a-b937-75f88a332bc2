import json
from copy import deepcopy
from enum import Enum


class Node:

    def __init__(self, id_=None, labels=None, properties=None):
        """
        Create a new node
        """
        self.id = id_
        self.labels = deepcopy(labels) if labels is not None else []
        self.properties = properties if properties is not None else {}

    @property
    def name(self):
        return self.properties.get('name')

    def __eq__(self, other):
        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not self.__eq__(other)

    def __str__(self):
        return str(self.__dict__)

    def to_json(self):
        return self.__dict__

    def __hash__(self):
        return hash(self.properties.get('_eid'))


class Edge:

    def __init__(self, src_node: Node, relation: str, dest_node: Node, id_=None, properties=None):
        """
        Create a new edge
        """
        self.id = id_
        self.relation = relation
        self.properties = properties if properties is not None else {}
        self.src_node = src_node
        self.dest_node = dest_node

    def __eq__(self, other):
        return self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not self.__eq__(other)

    def __str__(self):
        data = {
            'id': self.id,
            'relation': self.relation,
            'properties': self.properties,
            'src_node': self.src_node.to_json(),
            'dest_node': self.dest_node.to_json()
        }
        return json.dumps(data, ensure_ascii=False)

    def to_json(self):
        return self.__dict__

    def __hash__(self):
        return hash(self.id)


class EdgeTypes(Enum):
    """
    边类型：全部，入边，出边
    """
    ALL = 0
    IN = 1
    OUT = 2
