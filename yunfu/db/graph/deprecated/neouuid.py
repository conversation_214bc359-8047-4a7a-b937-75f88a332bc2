import re
import uuid as _uuid
from typing import Union

from yunfu.common import LogUtils

logger = LogUtils.get_logger(__name__)


class Neo4jUuid(object):
    """neo4j实体和关系uuid生成类"""

    @classmethod
    def get_entity_uuid(cls, name: Union[str, int, float]) -> Union[None, str]:
        """
        生成实体uuid

        :param name: 实体名
        :return uuid
        :rtype: None or str
        """
        if not name:
            logger.error('name not exists')
            return None
        source = str(name)
        return str(_uuid.uuid3(_uuid.NAMESPACE_DNS, source))

    @classmethod
    def get_relation_uuid(cls, src_name: Union[str, int, float], r_name: Union[str, int, float],
                          dest_name: Union[str, int, float]) -> Union[None, str]:
        """
        生成关系uuid

        :param src_name: 源实体名
        :param r_name: 关系名
        :param dest_name: 目的实体名
        :return uuid
        :rtype: None or str
        """
        parames = {'src_name': src_name, 'r_name': r_name, 'dest_name': dest_name}
        for key, name in parames.items():
            if not name:
                logger.error('{0} not exists'.format(key))
                return None
        source = str(src_name) + str(r_name) + str(dest_name)
        return str(_uuid.uuid3(_uuid.NAMESPACE_DNS, source))

    @classmethod
    def is_legal_uuid(cls, uuid: str) -> bool:
        """
        判断uuid是否合法

        :param uuid: uuid值
        :return 是否合法
        :rtype: bool
        """
        if not uuid:
            logger.error('{0} not exists'.format(uuid))
            return False
        matcher = re.match(r'[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}', str(uuid))
        return True if matcher else False
