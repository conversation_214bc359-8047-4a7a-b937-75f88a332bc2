from abc import ABC, abstractmethod
from typing import List

from yunfu.db.graph.deprecated.models import Edge, EdgeTypes, Node


class BaseClient(ABC):
    """
    图数据库操作抽象类
    """

    @abstractmethod
    def create_fulltext_index(
        self, index_name: str, labels: List[str], field_names: List[str]
    ) -> None:
        """创建全文索引

        :param index_name: 全文索引名称
        :param labels: 标签列表
        :param field_names: 索引字段名称列表
        :return None
        :rtype: None
        """
        raise NotImplementedError

    @abstractmethod
    def delete_fulltext_index(self, index_name: str) -> None:
        """删除全文索引

        :param index_name: 全文索引名称
        :return None
        :rtype: None
        """
        raise NotImplementedError

    @abstractmethod
    def search_nodes(
        self, index_name: str, query: str, skip: int = 0, limit: int = 20
    ) -> List[Node]:
        """使用全文索引搜索节点

        :param index_name: 全文索引名称
        :param query: 检索词
        :param skip: 偏移量
        :param limit: 返回数据量
        :return 节点列表
        :rtype: typing.List[models.Node]
        """
        raise NotImplementedError

    @abstractmethod
    def get_node(self, id_: int) -> Node:
        """根据节点id获取节点

        :param id_: 节点ID
        :return 节点
        :rtype: models.Node
        """
        raise NotImplementedError

    @abstractmethod
    def get_node_by_uuid(self, uuid: str) -> Node:
        """根据节点uuid获取节点

        :param uuid: 节点uuid
        :return 节点
        :rtype: models.Node
        """
        raise NotImplementedError

    @abstractmethod
    def get_nodes(
        self, skip: int = 0, limit: int = 20, *labels, **properties
    ) -> List[Node]:
        """根据节点属性获取节点

        :param skip: 偏移量
        :param limit: 返回数据量
        :param *labels: 标签
        :param *properties: 属性
        :return 节点列表
        :rtype: typing.List[models.Node]
        """
        raise NotImplementedError

    @abstractmethod
    def create_node(self, node: Node) -> Node:
        """创建节点(会重复创建节点)
        无论是否存在节点，均会创建

        :param node: 待新建节点
        :type node: models.Node
        :return: 创建好的新节点
        :rtype: models.Node
        """
        raise NotImplementedError

    def save_node(self, node: Node) -> Node:
        """保存节点(不会重复创建节点)
        如果节点存在则更新节点，不存在则创建

        :param node: 待新建节点
        :type node: models.Node
        :return: 创建好的新节点或已有节点
        :rtype: models.Node
        """
        raise NotImplementedError

    @abstractmethod
    def update_node(self, node: Node) -> Node:
        """更新节点
        更新节点标签、属性，会整体替换

        :param node: 待更新节点
        :type node: models.Node
        :return: 更新好的新节点
        :rtype: models.Node
        """
        raise NotImplementedError

    @abstractmethod
    def partial_update_node(self, node: Node) -> Node:
        """部分更新节点
        只更新传入的数据，标签为没有则添加，属性为有则更新、没有则创建

        :param node: 待更新节点
        :type node: models.Node
        :return: 更新好的新节点
        :rtype: models.Node
        """
        raise NotImplementedError

    @abstractmethod
    def delete_node(self, node: Node) -> None:
        """删除节点

        :param node: 待删除节点
        :type node: models.Node
        :return None
        :rtype: None
        """
        raise NotImplementedError

    @abstractmethod
    def get_edge(self, id_: int) -> Edge:
        """查询边

        :param id_: 边id
        :return 边
        :rtype: models.Edge
        """
        raise NotImplementedError

    @abstractmethod
    def get_edges(
        self,
        node: Node,
        type: EdgeTypes = EdgeTypes.ALL,
        skip: int = 0,
        limit: int = 20,
    ) -> List[Edge]:
        """查询节点边

        :param node: 节点
        :type node: models.Node
        :param type: 获取边类型，全部、入边、出边
        :param skip: 偏移量
        :param limit: 返回数据量
        :return 边列表
        :rtype: typing.List[models.Edge]
        """
        raise NotImplementedError

    @abstractmethod
    def create_edge(self, edge: Edge) -> Edge:
        """创建边(会重复创建边)
        无论是否存在边，均会创建

        :param edge: 待创建边
        :type edge: models.Edge
        :return 创建好的边
        :rtype: models.Edge
        """
        raise NotImplementedError

    @abstractmethod
    def save_edge(self, edge: Edge) -> Edge:
        """保存边(不会重复创建边)
        如果边存在则更新边，不存在则创建

        :param edge: 待创建边
        :type edge: models.Edge
        :return 创建好的边或已存在的边
        :rtype: models.Edge
        """
        raise NotImplementedError

    @abstractmethod
    def update_edge(self, edge: Edge) -> Edge:
        """更新边
        更新边属性，会整体替换

        :param edge: 待更新边
        :type edge: models.Edge
        :return: 更新好的边
        :rtype: models.Edge
        """
        raise NotImplementedError

    @abstractmethod
    def partial_update_edge(self, edge: Edge) -> Edge:
        """部分更新边
        只更新传入的数据，属性为有则更新、没有则创建

        :param edge: 待更新边
        :type edge: models.Edge
        :return: 更新好的边
        :rtype: models.Edge
        """
        raise NotImplementedError

    @abstractmethod
    def delete_edge(self, edge: Edge) -> None:
        """删除边

        :param edge: 待删除边
        :type edge: models.Edge
        :return None
        :rtype: None
        """
        raise NotImplementedError

    @abstractmethod
    def count_nodes(self, labels: List[str]) -> int:
        """统计节点数

        :param labels: 标签列表
        :type labels: List[str]
        :return: [节点数]
        :rtype: int
        """
        raise NotImplementedError

    @abstractmethod
    def count_edges(self, labels: List[str], type_: EdgeTypes = EdgeTypes.ALL) -> int:
        """统计边数

        :param labels: 标签列表
        :type labels: List[str]
        :param type_: 边类型, defaults to EdgeTypes.ALL
        :type type_: EdgeTypes, optional
        :return: 边数
        :rtype: int
        """
        raise NotImplementedError
