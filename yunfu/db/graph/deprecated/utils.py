import re
import time


class TimeUtils:

    @classmethod
    def now_str(cls) -> str:
        """
        格式化成2016-03-20 11:45:39形式
        """
        return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())


class VersionManager:

    @classmethod
    def validate(cls, version: str):
        """版本格式检查"""
        if not isinstance(version, str):
            raise ValueError(f'版本格式不支持: {version}')
        version = version.lower()
        if version != 'c' and not cls.is_released_version(version):
            raise ValueError(f'版本格式不支持: {version}')
        return version

    @staticmethod
    def is_released_version(version: str):
        """是否是历史版本"""
        return re.match(r'v\d+', version) is not None

    @classmethod
    def has_released_version(cls, versions):
        """检测versions Sequence中是否有符合标准的version"""
        for version in versions:
            if not isinstance(version, str):
                raise ValueError(f'版本格式不支持: {version}')
            if cls.is_released_version(version):
                return True
        return False
