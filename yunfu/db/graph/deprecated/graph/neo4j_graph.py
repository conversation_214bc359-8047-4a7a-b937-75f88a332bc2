import logging
import re
from copy import deepcopy
from itertools import islice
from typing import Any, Dict, List, Optional, Union

from py2neo import Graph
from py2neo import Node as neoNode
from py2neo import NodeMatcher, Relationship, RelationshipMatcher
from py2neo.bulk import create_nodes, create_relationships
from yunfu.common import LogUtils

from yunfu.db.graph.deprecated.executors import EdgeCreateExecutor, NodeCreateExecutor
from yunfu.db.graph.deprecated.graph.graph import YFEditableGraph, YFRetrieveGraph
from yunfu.db.graph.deprecated.match import YFEdgeMatcherForNeo4j, YFNodeMatcherForNeo4j
from yunfu.db.graph.deprecated.models import Edge, Node
from yunfu.db.graph.deprecated.utils import VersionManager

logger = LogUtils.get_logger(__name__)
logging.getLogger("py2neo.batch").setLevel(logging.WARNING)
logging.getLogger("py2neo.cypher").setLevel(logging.WARNING)
logging.getLogger("py2neo.client").setLevel(logging.WARNING)
logging.getLogger("faker.factory").setLevel(logging.WARNING)


class Neo4jGraph(YFRetrieveGraph, YFEditableGraph):

    py2neo_graph = None

    def __init__(self, conf, space=None, use_version=False, stat_client=None):
        if not space:
            raise ValueError("space不能为空")
        self._conf = conf
        self._space = space
        if Neo4jGraph.py2neo_graph is None:
            Neo4jGraph.py2neo_graph = Graph(
                f"bolt://{conf['host']}:{conf['port']}",
                auth=(conf["username"], conf["password"]),
            )
        self._graph = Neo4jGraph.py2neo_graph
        self._use_version = use_version
        self._current_version = conf.get("current_version", "c")
        self._version = self._current_version
        self.node_create_executor = NodeCreateExecutor(self._graph, conf, stat_client)
        self.edge_create_executor = EdgeCreateExecutor(self._graph, conf, stat_client)

    @property
    def use_version(self):
        return self._use_version

    @property
    def graph(self):
        return self._graph

    @property
    def space(self):
        return self._space

    @property
    def version(self):
        return self._current_version

    @property
    def nodes(self):
        return YFNodeMatcherForNeo4j(self)

    @property
    def edges(self):
        return YFEdgeMatcherForNeo4j(self)

    def run(self, cypher: str, parameters=None, **kwparameters):
        return self._graph.run(cypher, parameters, **kwparameters)

    def create(self, obj) -> Union[Node, Edge, List[Union[Node, Edge]]]:
        result_list = []
        if isinstance(obj, Node):
            return self._create_node(obj)
        if isinstance(obj, Edge):
            return self._create_edge(obj)
        if isinstance(obj, list):
            node_list, edge_list = [], []
            for obj_ in obj:
                if isinstance(obj_, Node):
                    node_list.append(obj_)
                elif isinstance(obj_, Edge):
                    edge_list.append(obj_)
                else:
                    raise TypeError(f"不支持的类型{type(obj_)}")
            for node in node_list:
                result_list.append(self._create_node(node))
            for edge in edge_list:
                result_list.append(self._create_edge(edge))
            return result_list
        raise TypeError("传入类型错误")

    def _create_node(self, node: Node) -> Node:
        if isinstance(node.labels, str):
            node.labels = [node.labels]
        if self._use_version:
            node.labels.append(self._current_version)
        if self._space not in node.labels:
            node.labels.append(self._space)
        neo_node = self._serializer_node(node)
        self._graph.create(neo_node)
        return self._parse_node(neo_node)

    def _create_edge(self, edge: Edge) -> Edge:
        properties = edge.properties
        node1 = self._get_node(edge.src_node)
        node2 = self._get_node(edge.dest_node)
        if not node1:
            raise ValueError("node1不存在")
        if not node2:
            raise ValueError("node2不存在")
        if self._use_version:
            properties[self._current_version] = True
        properties["_space"] = self._space
        relation_query = ""
        for property_, value in properties.items():
            if isinstance(value, str):
                value = value.replace("'", "\\'")
                relation_query += f" SET r.`{property_}`='{value}' "
            elif value is None:
                relation_query += f" SET r.`{property_}`=Null "
            else:
                relation_query += f" SET r.`{property_}`={value} "
        query = f"match (n1:{':'.join(node1.labels)}), (n2:{':'.join(node2.labels)}) where ID(n1)={node1.identity} and ID(n2)={node2.identity} create p=(n1)-[r:`{edge.relation}`]->(n2) {relation_query} return r"  # noqa
        results = self._graph.run(query).data()
        new_edge = results[0]["r"]
        return self._parse_edge(self._get_edge(self._parse_edge(new_edge)))

    def delete(self, obj):  # noqa
        """删除节点/边

        :param obj: 点/边/点集/边集
        :type obj: Node/Edge/List[Node]/List[Edge]
        :return 建立成功的Node/Edge/List[Node]/List[Edge]
        """
        # TODO 删除的返回值
        result_list = []
        if isinstance(obj, Node):
            return self._delete_node(obj)
        if isinstance(obj, Edge):
            return self._delete_edge(obj)
        if isinstance(obj, list):
            edge_list, node_list = [], []
            for obj_ in obj:
                if isinstance(obj_, Node):
                    node_list.append(obj_)
                elif isinstance(obj_, Edge):
                    edge_list.append(obj_)
                else:
                    raise TypeError(f"不支持的类型{type(obj_)}")
            for edge in edge_list:
                result_list.append(self._delete_edge(edge))
            for node in node_list:
                result_list.append(self._delete_node(node))
            return result_list
        raise ValueError("传入类型错误")

    def _delete_node(self, node: Node):
        if self._use_version:
            neo_node = self._get_node(node)
            if self._current_version not in neo_node.labels:
                raise ValueError(f"不支持删除历史版本节点: {node}")
            neo_node.remove_label(self._current_version)
            self._graph.push(neo_node)
            neo_edges = (
                self._graph.relationships.match(nodes=(neo_node,)).all()
                + self._graph.relationships.match(nodes=(None, neo_node)).all()
            )
            for neo_edge in neo_edges:
                neo_edge.update({self._current_version: False})
                self._graph.push(neo_edge)
        else:
            self._graph.delete(self._get_node(node))

    def _delete_edge(self, edge: Edge):  # noqa
        if self._use_version:
            neo_edge = self._get_edge(edge)
            properties = self._parse_edge(neo_edge).properties
            if self._current_version not in properties or not properties.get(
                self._current_version
            ):
                raise ValueError(f"不支持删除历史版本边: {neo_edge}")
            neo_edge.update({self._current_version: False})
            self._graph.push(neo_edge)
        else:
            self._graph.separate(self._get_edge(edge))

    def update(self, obj) -> Union[Node, Edge, List[Union[Node, Edge]]]:  # noqa
        """更新节点/边

        :param obj: 点/边/点集/边集
        :type obj: Node/Edge/List[Node]/List[Edge]
        :return 建立成功的Node/Edge/List[Node]/List[Edge]
        """
        result_list = []
        if isinstance(obj, Node):
            return self._update_node(obj)
        if isinstance(obj, Edge):
            return self._update_edge(obj)
        if isinstance(obj, list):
            for obj_ in obj:
                if isinstance(obj_, Node):
                    result_list.append(self._update_node(obj_))
                elif isinstance(obj_, Edge):
                    result_list.append(self._update_edge(obj_))
            return result_list
        raise ValueError("传入类型错误")

    def _update_node(self, node: Node) -> Node:  # noqa
        """
        不使用use_version:
            根据node获取neo_node, 更新neo_node属性, 更新neo_node, 返回对应node
        使用use_version:
            根据node获取neo_node, 判断neo_node是否存在_current_version当前版本号:
            不存在_current_version:
                抛出异常, 只能编辑_current_version的点
            存在_current_version:
                移除当前neo_node的_current_version标签,
                新建一个传入node的点,
                返回neo_node链接的所有边, 对于每一条边:
                    将旧边的_current_version置为false,
                    新建与新点链接边, 更新属性

        """
        neo_node = self._get_node(node)
        if self._use_version:
            if self._current_version not in neo_node.labels:
                raise ValueError(f"不支持编辑历史版本节点: {node}")
            if VersionManager.has_released_version(neo_node.labels):
                neo_node.remove_label(self._current_version)
                self._graph.push(neo_node)
                neo_edges = (
                    self._graph.relationships.match(nodes=(neo_node,)).all()
                    + self._graph.relationships.match(nodes=(None, neo_node)).all()
                )
                new_node = self._create_node(node)
                old_node = self._parse_node(neo_node)

                for neo_edge in neo_edges:
                    edge = self._parse_edge(neo_edge)
                    neo_edge.update({self._current_version: False})
                    self._graph.push(neo_edge)
                    if edge.src_node.id == old_node.id:
                        edge.src_node = new_node
                    elif edge.dest_node.id == old_node.id:
                        edge.dest_node = new_node
                    edge2: Edge = self.create(edge)
                    neo_edge2 = self._get_edge(edge2)

                    for name, value in edge2.properties.items():
                        if VersionManager.is_released_version(name):
                            value = None
                        neo_edge2.update({name: value})
                    self._graph.push(neo_edge2)
                node = new_node
            else:
                self._update_labels(node, neo_node)
                neo_node.add_label(self._current_version)
                for name, value in node.properties.items():
                    neo_node.update({name: value})
                self._graph.push(neo_node)
                node = self._parse_node(self._get_node(node))
        else:
            self._update_labels(node, neo_node)
            for name, value in node.properties.items():
                neo_node.update({name: value})
            self._graph.push(neo_node)
            node = self._parse_node(self._get_node(node))
        return node

    def _update_labels(self, node: Node, neo_node: neoNode):
        neo_node.clear_labels()
        labels = deepcopy(node.labels)
        if self._space not in labels:
            labels.append(self._space)
        neo_node.update_labels(labels)

    def _update_edge(self, edge: Edge) -> Edge:  # noqa
        neo_edge = self._get_edge(edge)
        if self._use_version:
            if self._current_version not in neo_edge or not neo_edge.get(
                self._current_version
            ):
                raise ValueError(f"不支持删除历史版本边: {neo_edge}")
            if VersionManager.has_released_version(neo_edge.keys()):
                neo_edge.update({self._current_version: False})
                self._graph.push(neo_edge)
                return self._create_edge(edge)
            for name, value in edge.properties.items():
                neo_edge.update({name: value})
            self._graph.push(neo_edge)
            return self._parse_edge(self._get_edge(edge))
        for name, value in edge.properties.items():
            neo_edge.update({name: value})
        self._graph.push(neo_edge)
        return self._parse_edge(self._get_edge(edge))

    def release(self, obj, version) -> Union[Node, Edge, List]:
        result_list = []
        if isinstance(obj, Node):
            return self._release_node(obj, version)
        if isinstance(obj, Edge):
            return self._release_edge(obj, version)
        if isinstance(obj, list):
            node_list, edge_list = [], []
            for obj_ in obj:
                if isinstance(obj_, Node):
                    node_list.append(obj_)
                elif isinstance(obj_, Edge):
                    edge_list.append(obj_)
                else:
                    raise TypeError(f"不支持的类型{type(obj_)}")
            for node in node_list:
                result_list.append(self._release_node(node, version))
            for edge in edge_list:
                result_list.append(self._release_edge(edge, version))
            return result_list
        raise TypeError("传入类型错误")

    def _release_node(self, node, version):
        # 发布版本时点增加历史版本
        neo_node = self._get_node(node)
        neo_node.add_label(version)
        self.graph.push(neo_node)
        return self._parse_node(neo_node)

    def _release_edge(self, edge, version):
        # 发布版本时边增加历史版本
        neo_edge = self._get_edge(edge)
        neo_edge.update({version: True})
        self.graph.push(neo_edge)
        return self._parse_edge(neo_edge)

    # TODO nodes没有去重
    def dfs(
        self,
        node: Node,
        degree: int,
        filters: Dict[str, Dict[str, Any]] = {},
        skip=0,
        limit=100,
    ):
        """以node为起点获取度为degree的子图

        :param node: 节点
        :type node: Node
        :param degree: 搜索深度
        :type degree: int
        """
        filter_list = []
        for part in filters.keys():
            filter_list.append(
                "AND".join(
                    [
                        (
                            f" {part}.{k} IN {v[1]}"
                            if v[0] == "list"
                            else f' {part}.{k} = "{v[1]}"'
                        )
                        for k, v in filters.get(part, {}).items()
                    ]
                )
            )
        filter_list = [item for item in filter_list if item]
        filter_query = "AND" + "AND".join(filter_list) if filter_list else ""
        neo_node = self._get_node(node)
        query = (
            f"""MATCH p=(n:{':'.join(neo_node.labels)})-[r*1..{degree}]-(m:{':'.join(neo_node.labels)}) """
            f"""where id(n) = {neo_node.identity} {filter_query} RETURN nodes(p) as nodes,"""
            f"""relationships(p) as edges SKIP {skip} LIMIT {limit}"""
        )
        results = self._graph.run(query).data()
        nodes = []
        edges = []
        for result in results:
            nodes.extend(
                [self._parse_node(graph_node) for graph_node in result["nodes"]]
            )
            edges.extend(
                [self._parse_edge(graph_edge) for graph_edge in result["edges"]]
            )
        if not nodes:
            nodes.append(self._get_node(node))
        return nodes, edges

    def path(
        self,
        start_node: Node,
        end_node: Node,
        r_types: Optional[List[str]] = None,
        degree: int = 1,
    ):
        """获取两点间
        :param start_node: 开始结点
        :type start_node: Node
        :param end_node: 终止节点
        :type end_node: Node
        :param degree: 路径长度
        :type degree: int
        :param r_types__in: 边类型
        :type r_types__in: str
        :param name: 边类型
        :type name: List[str]
        """
        """match_query = f"MATCH p=(n:{label})-[r:属于]->(m:{label}) WHERE n._eid='{}' AND m._eid='{}' "
        where_query = f''
        query = f'''{match_query} RETURN p'''
        results = self._graph.run(query).data()
        return [(self._parse_path(result[0])) for result in results]"""
        pass

    def node_count(self, labels: List[str]) -> int:
        """返回图中包含labels的点的总数

        :param labels: 点的labels
        :type labels: List[str]
        """
        labels = self._process_labels(labels)
        label_list = ":".join(labels)
        query = f"MATCH (n:{label_list}) return count(n) as count"
        results = self._graph.run(query).data()
        count: int = results[0]["count"]
        return int(count)

    def edge_count(self, labels: List[str]) -> int:
        labels = self._process_labels(labels)
        label_list = ":".join(labels)
        query = f"MATCH(n:{label_list})-[r]->(m:{label_list}) return count(r) as count"
        results = self._graph.run(query).data()
        count: int = results[0]["count"]
        return int(count)

    def property_count(self, labels: List[str]) -> int:
        """返回图中包含labels的点的属性的总数

        :param labels: 点的labels
        :type labels: List[str]
        """
        labels = self._process_labels(labels)
        label_list = ":".join(labels)
        query = f"MATCH(n:{label_list}) UNWIND KEYS(n) as key RETURN key, count(*)"
        results = self._graph.run(query)
        count = 0
        for item in results:
            count += item["count(*)"]
        return count

    def _process_labels(self, labels: List[str]):
        labels = deepcopy(labels)
        if self._use_version:
            labels.append(self._version)
        labels.append(self._space)
        labels = list(set(labels))
        return labels

    def get_history_graph(self, version: str) -> YFRetrieveGraph:
        return Neo4jReadonlyGraph(self._conf, self._space, version)

    def get_versions(self) -> List[str]:
        query = f"match (n:{self._space}) return distinct(labels(n)) as labels"
        results = self._graph.run(query).data()
        versions = []
        for result in results:
            for version in result["labels"]:
                if self._is_version(version):
                    versions.append(version)
        return list(set(versions))

    def clear(self, labels: List[str]):
        """清除指定labels的点及相关边

        :param labels: 待清除labels
        :type labels: List[str]
        """
        label_list = ":".join(labels)
        query = f"MATCH (n:{label_list}) detach delete n"
        self._graph.run(query)

    def _get_node(self, node: Node):
        if not node:
            return None
        matcher = NodeMatcher(self._graph)
        labels = [self._space]
        if self._use_version:
            labels.append(self._version)
        if node.id:
            neo_node = matcher.get(node.id)
        elif "_eid" in node.properties.keys():
            neo_node = matcher.match(*labels, _eid=node.properties["_eid"]).first()
        else:
            raise ValueError("传入的点需要有_eid或id")
        if neo_node:
            neo_node._remote_labels = frozenset(neo_node.labels)
        return neo_node

    def _get_edge(self, edge: Edge):
        matcher = RelationshipMatcher(self._graph)
        if edge.id:
            edge = matcher.get(edge.id)
        else:
            edge = matcher.match(
                {self._get_node(edge.src_node), self._get_node(edge.dest_node)},
                edge.relation,
                **edge.properties,
            ).first()
        return edge

    def _serializer_node(self, node: Node):
        return neoNode(*node.labels, **node.properties)

    def _serializer_edge(self, edge: Edge):
        return Relationship(
            self._get_node(edge.src_node),
            edge.relation,
            self._get_node(edge.dest_node),
            **edge.properties,
        )

    def _parse_node(self, node: neoNode):
        if not node:
            return None
        labels = node._labels
        if self._space in labels:
            labels.remove(self._space)
        new_node = Node(id_=node.identity, labels=list(labels), properties=dict(node))
        return new_node

    def _parse_edge(self, relationship: Relationship):
        return Edge(
            self._parse_node(relationship.start_node),
            type(relationship).__name__,
            self._parse_node(relationship.end_node),
            relationship.identity,
            deepcopy(dict(relationship)),
        )

    def _is_version(self, version: str) -> bool:
        pattern = "v[0-9]+"
        if re.match(pattern, version.lower()):
            return True
        return False

    # TODO 家乐 补充单元测试
    @property
    def node_labels(self) -> set:
        return self._graph.schema.node_labels  # type: ignore

    @property
    def relationship_types(self) -> set:
        return self._graph.schema.relationship_types  # type: ignore

    def create_nodes(
        self, nodes: List[Node], sync: bool = True, batch_size: int = 1000
    ):
        """批量创建点"""
        labels2nodes: dict = {}
        for node in nodes:
            labels2nodes.setdefault(tuple(node.labels), []).append(node.properties)
        if sync:
            for labels, nodes in labels2nodes.items():
                labels = list(labels)
                labels.append(self._space)
                if self._use_version:
                    labels.append(self._current_version)
                stream = iter(nodes)
                while True:
                    batch = islice(stream, batch_size)
                    if not list(deepcopy(batch)):
                        break
                    create_nodes(self._graph.auto(), batch, labels)
        else:
            for labels, nodes in labels2nodes.items():
                labels = list(labels)
                labels.append(self._space)
                if self._use_version:
                    labels.append(self._current_version)
                for node in nodes:
                    self.node_create_executor.add_task(node=node, labels=labels)

    def create_edges(
        self, edges: List[Edge], sync: bool = True, batch_size: int = 1000
    ):
        """批量创建边"""
        type2edges: dict = {}
        for edge in edges:
            edge.properties["_space"] = self._space
            if self._use_version:
                edge.properties[self.version] = True
            type2edges.setdefault(edge.relation, []).append(
                (edge.src_node.id, edge.properties, edge.dest_node.id)
            )
        if sync:
            for r_type, edges in type2edges.items():
                steam = iter(edges)
                while True:
                    batch = islice(steam, batch_size)
                    if not list(deepcopy(batch)):
                        break
                    create_relationships(self._graph.auto(), batch, r_type)
        else:
            for r_type, edges in type2edges.items():
                for edge in edges:
                    self.edge_create_executor.add_task(edge=edge, r_type=r_type)

    def create_index(self, label: str, properties: List[str]):
        """创建索引"""
        self._graph.schema.create_index(label, *properties)

    def create_unique_constraint(self, label: str, properties: List[str]):
        """创建唯一约束"""
        self._graph.schema.create_uniqueness_constraint(label, *properties)

    def drop_index(self, label: str, properties: List[str]):
        """删除索引"""
        self._graph.schema.drop_index(label, *properties)

    def drop_unique_constraint(self, label: str, properties: List[str]):
        """删除唯一约束"""
        self._graph.schema.drop_uniqueness_constraint(label, *properties)

    def get_indexes(self, label):
        """获取所有索引"""
        return self._graph.schema.get_indexes(label)

    def get_uniqueness_constraints(self, label):
        """获取所有唯一约束"""
        return self._graph.schema.get_uniqueness_constraints(label)


class Neo4jReadonlyGraph(YFRetrieveGraph):
    py2neo_graph = None

    def __init__(self, conf, space=None, version="c", use_version=True):
        if not space:
            raise ValueError("space不能为空")
        if not self._is_version(version):
            raise ValueError("传入version格式错误")
        self._space = space
        if Neo4jReadonlyGraph.py2neo_graph is None:
            Neo4jReadonlyGraph.py2neo_graph = Graph(
                f"bolt://{conf['host']}:{conf['port']}",
                auth=(conf["username"], conf["password"]),
            )
        self._graph = Neo4jReadonlyGraph.py2neo_graph
        self._use_version = use_version
        self._version = version
        self._current_version = conf.get("current_version", "c")

    @property
    def use_version(self):
        return self._use_version

    @property
    def graph(self):
        return self._graph

    @property
    def space(self):
        return self._space

    @property
    def version(self):
        return self._version

    @property
    def nodes(self):
        return YFNodeMatcherForNeo4j(self)

    @property
    def edges(self):
        return YFEdgeMatcherForNeo4j(self)

    def node_count(self, labels: List[str]):
        """返回图中包含labels的点的总数

        :param labels: 点的labels
        :type labels: List[str]
        """
        labels = self._process_labels(labels)
        label_list = ":".join(labels)
        query = f"MATCH (n:{label_list}) return count(n) as count"
        results = self._graph.run(query).data()
        count = results[0]["count"]
        return count

    def edge_count(self, labels: List[str]):
        labels = self._process_labels(labels)
        label_list = ":".join(labels)
        query = f"MATCH(n:{label_list})-[r]->(m:{label_list}) return count(r) as count"
        results = self._graph.run(query).data()
        count = results[0]["count"]
        return count

    def property_count(self, labels: List[str]):
        """返回图中包含labels的点的属性的总数

        :param labels: 点的labels
        :type labels: List[str]
        """
        labels = self._process_labels(labels)
        label_list = ":".join(labels)
        query = f"MATCH(n:{label_list}) UNWIND KEYS(n) as key RETURN key, count(*)"
        results = self._graph.run(query)
        count = 0
        for item in results:
            count += item["count(*)"]
        return count

    def dfs(self, node: Node, degree: int):
        """以node为起点获取度为degree的子图

        :param node: 节点
        :type node: Node
        :param degree: 搜索深度
        :type degree: int
        """
        labels = self._process_labels(node.labels)
        label_list = ":".join(labels)
        query = (
            f"MATCH p=(n:{label_list})-[r*1..{degree}]-(m:{label_list}) "
            f"where n._eid='{node.properties['_eid']}' RETURN nodes(p) as nodes,relationships(p) as edges"
        )
        results = self._graph.run(query).data()
        nodes = []
        edges = []
        for result in results:
            nodes.extend(
                [self._parse_node(graph_node) for graph_node in result["nodes"]]
            )
            edges.extend(
                [self._parse_edge(graph_edge) for graph_edge in result["edges"]]
            )
        if not nodes:
            nodes.append(self._get_node(node))
        return nodes, edges

    def path(
        self,
        start_node: Node,
        end_node: Node,
        r_types: Optional[List[str]] = None,
        degree: int = 1,
    ):
        """获取两点间
        :param start_node: 开始结点
        :type start_node: Node
        :param end_node: 终止节点
        :type end_node: Node
        :param degree: 路径长度
        :type degree: int
        :param r_types__in: 边类型
        :type r_types__in: str
        :param name: 边类型
        :type name: List[str]
        """
        """match_query = f"MATCH p=(n:{label})-[r:属于]->(m:{label}) WHERE n._eid='{}' AND m._eid='{}' "
        where_query = f''
        query = f'''{match_query} RETURN p'''
        results = self._graph.run(query).data()
        return [(self._parse_path(result[0])) for result in results]"""
        pass

    def _is_version(self, version: str) -> bool:
        pattern = "v[0-9]+"
        if re.match(pattern, version.lower()):
            return True
        return False

    def _process_labels(self, labels: List[str]):
        labels = deepcopy(labels)
        if self._use_version:
            labels.append(self._version)
        labels.append(self._space)
        labels = list(set(labels))
        return labels

    def _get_node(self, node: Node):
        if not node:
            return None
        matcher = NodeMatcher(self._graph)
        labels = [self._space]
        if self._use_version:
            labels.append(self._version)
        if node.id:
            neo_node = matcher.get(node.id)
        elif "_eid" in node.properties.keys():
            neo_node = matcher.match(*labels, _eid=node.properties["_eid"]).first()
        else:
            raise ValueError("传入的点需要有_eid或id")
        if neo_node:
            neo_node._remote_labels = frozenset(neo_node.labels)
        return neo_node

    def _parse_node(self, node: neoNode):
        if not node:
            return None
        labels = node._labels
        if self._space in labels:
            labels.remove(self._space)
        new_node = Node(id_=node.identity, labels=list(labels), properties=dict(node))
        return new_node

    def _parse_edge(self, relationship: Relationship):
        return Edge(
            self._parse_node(relationship.start_node),
            type(relationship).__name__,
            self._parse_node(relationship.end_node),
            relationship.identity,
            deepcopy(dict(relationship)),
        )
