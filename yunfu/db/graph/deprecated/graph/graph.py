from abc import ABC, abstractmethod
from typing import List

from yunfu.db.graph.deprecated.models import Node


class YFRetrieveGraph(ABC):

    @abstractmethod
    def node_count(self, labels: List[str]):
        """返回图中包含labels的点的总数

        :param labels: 点的labels
        :type labels: List[str]
        """
        raise NotImplementedError

    @abstractmethod
    def edge_count(self, labels: List[str]):
        """返回图中包含labels的点间的边总数(交集)

        :param labels: 边的两个端点的labels
        :type labels: List[str]
        """
        raise NotImplementedError

    @abstractmethod
    def property_count(self, labels: List[str]):
        """返回图中包含labels的点的属性的总数

        :param labels: 点的labels
        :type labels: List[str]
        """
        raise NotImplementedError

    @abstractmethod
    def dfs(self, node: Node, depth: int):
        """以node为起点获取深度为depth的子图

        :param node: 节点
        :type node: Node
        :param depth: 搜索深度
        :type depth: int
        """
        raise NotImplementedError

    @abstractmethod
    def path(self, start_node: Node, end_node: Node, r_types__in: List[str]):
        """获取两点间经历的路径

        :param start_node: 开始结点
        :type start_node: Node
        :param end_node: 终止节点
        :type end_node: Node
        :param r_types__in: 边类型
        :type r_types__in: List[str]
        """
        raise NotImplementedError


class YFEditableGraph(ABC):
    @abstractmethod
    def create(self, obj):
        """创建节点/边

        :param obj: 点/边/点集/边集
        :type obj: Node/Edge/List[Node]/List[Edge]
        :return 建立成功的Node/Edge/List[Node]/List[Edge]
        """
        raise NotImplementedError

    @abstractmethod
    def delete(self, obj):
        """删除节点/边

        :param obj: 点/边/点集/边集
        :type obj: Node/Edge/List[Node]/List[Edge]
        :return 建立成功的Node/Edge/List[Node]/List[Edge]
        """
        raise NotImplementedError

    @abstractmethod
    def update(self, obj):
        """更新节点/边

        :param obj: 点/边/点集/边集
        :type obj: Node/Edge/List[Node]/List[Edge]
        :return 建立成功的Node/Edge/List[Node]/List[Edge]
        """
        raise NotImplementedError

    @abstractmethod
    def get_versions(self) -> List[str]:
        """获取所有历史版本

        :return: 历史版本列表
        :rtype: List[str]
        """
        raise NotImplementedError

    @abstractmethod
    def get_history_graph(self, version: str) -> YFRetrieveGraph:
        """获取历史版本只读图

        :param version: 版本名
        :type version: str
        :return: 指定版本只读图
        :rtype: YFRetrieveGraph
        """
        raise NotImplementedError
