import time

from py2neo.bulk import create_nodes, create_relationships
from yunfu.common import DelayedBatchExecutor, LogUtils

logger = LogUtils.get_logger(__name__)


class NodeCreateExecutor(DelayedBatchExecutor):
    def __init__(self, graph, conf, stat_client) -> None:
        self._graph = graph
        self._stat_client = stat_client
        logger.info(f"stat_client: {self._stat_client and self._stat_client.__dict__}")
        super().__init__(
            num_threads=conf.get("num_threads", 1),
            batch_size=conf.get("batch_size", 100),
            duration=conf.get("duration", 0.1),
        )
        self._stat_conf = conf.get("stat_conf", {})
        logger.info(f"stat_client: {self._stat_conf}")

    def run(self, tasks):
        if self._stat_client:
            start_time = time.time()
        labels2nodes = {}
        for task in tasks:
            labels2nodes.setdefault(tuple(task.kwargs["labels"]), []).append(
                task.kwargs["node"]
            )
        for labels, nodes in labels2nodes.items():
            create_nodes(self._graph.auto(), nodes, labels)
        if self._stat_client:
            self._stat_client.timing(
                self._stat_conf.get("create_node_name", "yfproduct-yfkm-fusion"),
                time.time() - start_time,
                tags={"step": self._stat_conf.get("create_node_step", "create_node")},
            )


class EdgeCreateExecutor(DelayedBatchExecutor):
    def __init__(self, graph, conf, stat_client) -> None:
        self._graph = graph
        self._stat_client = stat_client
        super().__init__(
            num_threads=conf.get("num_threads", 1),
            batch_size=conf.get("batch_size", 100),
            duration=conf.get("duration", 0.1),
        )
        self._stat_conf = conf.get("stat_conf", {})

    def run(self, tasks):
        if self._stat_client:
            start_time = time.time()
        type2edges = {}
        for task in tasks:
            type2edges.setdefault(task.kwargs["r_type"], []).append(task.kwargs["edge"])
        for r_type, edges in type2edges.items():
            create_relationships(self._graph.auto(), edges, r_type)
        if self._stat_client:
            self._stat_client.timing(
                self._stat_conf.get("create_edge_name", "yfproduct-yfkm-fusion"),
                time.time() - start_time,
                tags={"step": self._stat_conf.get("create_edge_step", "create_edge")},
            )
