from abc import ABC, abstractmethod
from typing import List, Optional

from yunfu.db.graph.deprecated.models import Edge, Node


class YFNodeMatch:
    """
    YFNodeMatchForNeo4j是一个对点进行匹配的抽象，支持使用limit、skip、order_by、where等方法对匹配结果进行筛选，
    并通过all、first、count、exist等方法获取匹配结果。
    """

    @abstractmethod
    def all(self) -> List[Node]:
        """
        立即进行结算，并返回所有的匹配结果
        """
        raise NotImplementedError

    @abstractmethod
    def count(self) -> int:
        """
        立即进行结算，并返回匹配结果的数量
        """
        raise NotImplementedError

    @abstractmethod
    def exist(self) -> bool:
        """
        立即进行结算，并返回是否存在匹配结果
        """
        raise NotImplementedError

    @abstractmethod
    def first(self) -> Optional[Node]:
        """
        立即进行结算，并返回第一个匹配结果
        """
        raise NotImplementedError

    @abstractmethod
    def limit(self, limit: int) -> "YFNodeMatch":
        """
        添加limit限制，限制查询结果数量
        """
        raise NotImplementedError

    @abstractmethod
    def order_by(self, *properties) -> "YFNodeMatch":
        """
        添加order_by排序，支持多个排序条件
        """
        raise NotImplementedError

    @abstractmethod
    def skip(self, skip: int) -> "YFNodeMatch":
        """
        添加skip跳过，跳过前skip个结果
        """
        raise NotImplementedError

    @abstractmethod
    def where(self, *predicates, **properties) -> "YFNodeMatch":
        """
        支持使用where方法编写较为复杂的匹配条件，使用例子如下：
        match.where("_.name = '张三' and _.age > 20")
        """
        raise NotImplementedError


class YFNodeMatcher(ABC):
    """
    匹配器类，支持使用get方法根据id获取点，使用match方法获取YFNodeMatch对象
    """

    @abstractmethod
    def get(self, id) -> Optional[Node]:
        """
        获取id为id的点,不存在的话返回None
        """
        raise NotImplementedError

    @abstractmethod
    def match(self, *labels, **properties) -> YFNodeMatch:
        """
        返回一个YFNodeMatch对象
        """
        raise NotImplementedError


class YFEdgeMatch(ABC):
    """
    YFEdgeMatch类，用于匹配边，支持链式调用，最后通过all、first、count、exist等方法获取匹配结果。
    """

    @abstractmethod
    def all(self) -> List[Edge]:
        """
        立即结算，获取所有匹配结果
        """
        raise NotImplementedError

    @abstractmethod
    def count(self) -> int:
        """
        立即结算，获取匹配结果的数量
        """
        raise NotImplementedError

    @abstractmethod
    def exists(self) -> bool:
        """
        立即结算，判断是否存在匹配结果
        """
        raise NotImplementedError

    @abstractmethod
    def first(self) -> Optional[Edge]:
        """
        立即结算，获取第一个匹配结果
        """
        raise NotImplementedError

    @abstractmethod
    def limit(self, amount) -> "YFEdgeMatch":
        """
        限制匹配结果的数量
        """
        raise NotImplementedError

    @abstractmethod
    def order_by(self, *fields) -> "YFEdgeMatch":
        """
        添加匹配结果的排序
        """
        raise NotImplementedError

    @abstractmethod
    def skip(self, amount) -> "YFEdgeMatch":
        """
        跳过匹配结果的前amount个
        """
        raise NotImplementedError

    @abstractmethod
    def where(self, *predicates, **properties) -> "YFEdgeMatch":
        """
        添加匹配条件，用例：
        match.where("_.length>30")
        _代替被匹配的边
        """
        raise NotImplementedError


class YFEdgeMatcher(ABC):
    """
    匹配器类，用于匹配边。支持get方法通过id获得边，支持match方法匹配边。
    """

    @abstractmethod
    def get(self, id: int) -> Optional[Edge]:
        """
        根据id获取边，不存在的话返回None
        """
        raise NotImplementedError

    @abstractmethod
    def match(self, nodes, r_type, **properties) -> YFEdgeMatch:
        """
        返回一个YFEdgeMatch对象
        """
        raise NotImplementedError
