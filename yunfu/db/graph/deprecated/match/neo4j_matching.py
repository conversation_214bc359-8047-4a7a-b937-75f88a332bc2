from typing import List, Optional

import py2neo

from yunfu.db.graph.deprecated.match.matching import (
    Y<PERSON><PERSON><PERSON>Match,
    YFEdgeMatcher,
    YFNodeMatch,
    YFNodeMatcher,
)
from yunfu.db.graph.deprecated.models import Edge, Node


def neonode2yfnode(neonode: py2neo.Node) -> Optional[Node]:
    return (
        Node(neonode.identity, list(neonode.labels), dict(neonode)) if neonode else None
    )


def yfnode2neonode(yfnode: Node) -> Optional[py2neo.Node]:
    return py2neo.Node(*yfnode.labels, **yfnode.properties) if yfnode else None


class YFNodeMatchForNeo4j(YFNodeMatch):
    """
    YFNodeMatchForNeo4j是一个对点进行匹配的抽象，支持使用limit、skip、order_by、where等方法对匹配结果进行筛选，
    并通过all、first、count、exist等方法获取匹配结果。
    """

    def __init__(self, graph, *labels, **properties):
        """
        graph: YFGraphForNeo4j对象
        labels: 点的标签
        properties: 点的属性
        """
        self._graph = graph
        self._space = self._graph.space
        self._use_version = self._graph.use_version
        self._version = self._graph.version
        self.labels = labels + (self._space,)
        if self._use_version:
            self.labels = self.labels + (self._version,)
        self.properties = properties
        self.match: py2neo.NodeMatch = py2neo.NodeMatcher(self._graph.graph).match(
            *self.labels, **self.properties
        )

    def _node_handler(self, node: Optional[Node]) -> Optional[Node]:
        """对返回的点进行处理，去除space和version信息等"""
        # if node:
        #     while self._space in node.labels:
        #         node.labels.remove(self._space)
        return node

    def all(self) -> List[Node]:
        """
        立即进行结算，并返回所有的匹配结果
        """
        nodes: List[py2neo.Node] = self.match
        results = []
        for node in nodes:
            node = self._node_handler(neonode2yfnode(node))
            if node:
                results.append(node)
        return results

    def count(self) -> int:
        """
        立即进行结算，并返回匹配结果的数量
        """
        return int(self.match.count())

    def exist(self) -> bool:
        """
        立即进行结算，并返回是否存在匹配结果
        """
        return bool(self.match.exists())

    def first(self) -> Optional[Node]:
        """
        立即进行结算，并返回第一个匹配结果
        """
        node = self.match.first()
        return self._node_handler(neonode2yfnode(node))

    def limit(self, limit: int) -> "YFNodeMatchForNeo4j":
        """
        添加limit限制，限制查询结果数量
        """
        self.match = self.match.limit(limit)
        return self

    def order_by(self, *properties) -> "YFNodeMatchForNeo4j":
        """
        添加order_by排序，支持多个排序条件
        """
        self.match = self.match.order_by(*properties)
        return self

    def skip(self, skip: int) -> "YFNodeMatchForNeo4j":
        """
        添加skip跳过，跳过前skip个结果
        """
        self.match = self.match.skip(skip)
        return self

    def where(self, *predicates, **properties) -> "YFNodeMatchForNeo4j":
        """
        支持使用where方法编写较为复杂的匹配条件，使用例子如下：
        match.where("_.name = '张三' and _.age > 20")
        """
        self.match = self.match.where(*predicates, **properties)
        return self


class YFNodeMatcherForNeo4j(YFNodeMatcher):
    """
    匹配器类，支持使用get方法根据id获取点，使用match方法获取YFNodeMatch对象
    """

    def __init__(self, graph):
        """
        graph: YFGraphForNeo4j对象
        """
        self._graph = graph
        self._space = self._graph.space
        self._version = self._graph.version
        self.matcher: py2neo.NodeMatcher = py2neo.NodeMatcher(self._graph.graph)

    def get(self, id) -> Optional[Node]:
        """
        获取id为id的点,不存在的话返回None
        """
        return neonode2yfnode(self.matcher.get(id))

    def match(self, *labels, **properties) -> YFNodeMatchForNeo4j:
        """
        返回一个YFNodeMatch对象
        """
        return YFNodeMatchForNeo4j(self._graph, *labels, **properties)


def neorelationship2yfedge(neorelationship: py2neo.Relationship) -> Optional[Edge]:
    """
    将py2neo.Relationship对象转换为Edge对象
    """
    if neorelationship:
        start_node = neonode2yfnode(neorelationship.start_node)
        end_node = neonode2yfnode(neorelationship.end_node)
        if start_node and end_node:
            return Edge(
                start_node,
                type(neorelationship).__name__,
                end_node,
                neorelationship.identity,
                dict(neorelationship),
            )
    return None


def yfedge2neorelationship(yfedge: Edge) -> Optional[py2neo.Relationship]:
    """
    将Edge对象转换为py2neo.Relationship对象
    """
    return (
        py2neo.Relationship.type(yfedge.relation)(
            yfnode2neonode(yfedge.src_node),
            yfnode2neonode(yfedge.dest_node),
            **yfedge.properties,
        )
        if yfedge
        else None
    )


class YFEdgeMatchForNeo4j(YFEdgeMatch):
    """
    YFEdgeMatch类，用于匹配边，支持链式调用，最后通过all、first、count、exist等方法获取匹配结果。
    """

    def __init__(self, graph, nodes, r_type, **properties):
        """
        nodes: 长度小于等于2的Sequence对象：如果长度为1，则表示从该点出发的所有边；如果长度为2，则表示从第一个点到第二个点的所有边。
        或长度小于等于2的Set对象：若长度为1，则表示与该点有关的所有边；若长度为2，则表示两个点之间的所有边。
        graph: YFGraphForNeo4j对象
        properties: 边的属性
        """
        self._graph = graph
        self._space = self._graph.space
        self._use_version = self._graph.use_version
        self._version = self._graph.version
        self.properties = properties
        self.properties["_space"] = self._space
        if self._use_version:
            self.properties[self._version] = True
        self.r_type = r_type
        self.nodes = nodes
        if r_type is not None and not isinstance(r_type, str):
            raise TypeError(f"r_type must be a str or None, but got {type(r_type)}")
        neo_r_type = py2neo.Relationship.type(r_type) if r_type else None
        neo_nodes = (
            type(nodes)([self._graph._get_node(node) for node in nodes])
            if nodes
            else None
        )
        self.match = py2neo.RelationshipMatcher(self._graph.graph).match(
            neo_nodes, neo_r_type, **self.properties
        )

    def _edge_handler(self, edge: Optional[Edge]) -> Optional[Edge]:
        # if edge:
        #     if '_space' in edge.properties:
        #         del edge.properties['_space']
        return edge

    def all(self) -> List[Edge]:
        """
        立即结算，获取所有匹配结果
        """
        neorelationships: List[py2neo.Relationship] = self.match
        results = []
        for neorelationship in neorelationships:
            relation = self._edge_handler(neorelationship2yfedge(neorelationship))
            if relation:
                results.append(relation)
        return results

    def count(self) -> int:
        """
        立即结算，获取匹配结果的数量
        """
        return int(self.match.count())

    def exists(self) -> bool:
        """
        立即结算，判断是否存在匹配结果
        """
        return bool(self.match.exists())

    def first(self) -> Optional[Edge]:
        """
        立即结算，获取第一个匹配结果
        """
        neorelationship: py2neo.Relationship = self.match.first()
        return self._edge_handler(neorelationship2yfedge(neorelationship))

    def limit(self, amount) -> "YFEdgeMatchForNeo4j":
        """
        限制匹配结果的数量
        """
        self.match = self.match.limit(amount)
        return self

    def order_by(self, *fields) -> "YFEdgeMatchForNeo4j":
        """
        添加匹配结果的排序
        """
        self.match = self.match.order_by(*fields)
        return self

    def skip(self, amount) -> "YFEdgeMatchForNeo4j":
        """
        跳过匹配结果的前amount个
        """
        self.match = self.match.skip(amount)
        return self

    def where(self, *predicates, **properties) -> "YFEdgeMatchForNeo4j":
        """
        添加匹配条件，用例：
        match.where("_.length>30")
        _代替被匹配的边
        """
        self.match = self.match.where(*predicates, **properties)
        return self


class YFEdgeMatcherForNeo4j(YFEdgeMatcher):
    """
    匹配器类，用于匹配边。支持get方法通过id获得边，支持match方法匹配边。
    """

    def __init__(self, graph):
        """
        graph: YFGraphForNeo4j对象
        """
        self._graph = graph
        self._space = self._graph.space
        self.matcher = py2neo.RelationshipMatcher(self._graph.graph)

    def get(self, id: int) -> Optional[Edge]:
        """
        根据id返回边
        """
        return neorelationship2yfedge(self.matcher.get(id))

    def match(self, nodes=None, r_type=None, **properties) -> YFEdgeMatchForNeo4j:
        """
        返回一个YFEdgeMatchForNeo4j对象，用于匹配边
        """
        return YFEdgeMatchForNeo4j(self._graph, nodes, r_type, **properties)
