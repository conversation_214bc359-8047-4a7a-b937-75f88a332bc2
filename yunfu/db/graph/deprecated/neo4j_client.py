from typing import List, Optional

from neo4j import GraphDatabase
from neo4j.graph import Node as Neo4jNode
from neo4j.graph import Path as Neo4jPath
from neo4j.graph import Relationship as Neo4jEdge
from yunfu.common.utils import LogUtils

from yunfu.db.graph.deprecated.base_client import BaseClient
from yunfu.db.graph.deprecated.models import Edge, EdgeTypes, Node
from yunfu.db.graph.deprecated.utils import TimeUtils

logger = LogUtils.get_logger(__name__)


class Neo4jClient(BaseClient):
    """
    图数据库操作抽象类
    """

    def __init__(self, host: str, port: int, username: str, password: str):
        self.graph = GraphDatabase.driver(
            f"bolt://{host}:{port}", auth=(username, password)
        )

    def _create_node(self, *labels, **properties):
        """
        创建图数据库节点
        """
        labels_query = ":".join(labels)
        properties_query = ""
        for property_, value in properties.items():
            if isinstance(value, str):
                value = value.replace("'", "\\'")
                properties_query += f" SET n.`{property_}`='{value}' "
            elif value is None:
                properties_query += f" SET n.`{property_}`=Null "
            else:
                properties_query += f" SET n.`{property_}`={value} "
        query = f"CREATE (n:{labels_query}) {properties_query} RETURN n"
        with self.graph.session() as session:
            result = session.run(query)
            return result.single()[0]

    def _parse_node(self, neo4j_node: Neo4jNode) -> Node:
        """
        图数据库节点转化为自定义节点
        """
        return Node(
            neo4j_node.id, list(neo4j_node.labels), self._extract_properties(neo4j_node)
        )

    def _extract_properties(self, neo4j_node: Neo4jNode):
        """
        将图数据库节点属性转为字典
        """
        properties = {}
        for key, value in neo4j_node.items():
            properties[key] = value
        return properties

    def _get_node(self, id_: int) -> Neo4jNode:
        """
        获取图数据库节点
        """
        if id_ is None:
            raise ValueError("id is None.")
        if not isinstance(id_, int):
            raise ValueError("id not is int.")
        query = f"MATCH (n) WHERE ID(n)={id_} RETURN n"
        with self.graph.session() as session:
            result = session.run(query)
            result = result.single()
            if result is None:
                raise ValueError("node is None.")
            return result[0]

    def _set_update_node_property_query(
        self, method: str, neo4j_node: Neo4jNode, node: Node
    ) -> str:  # noqa
        """
        生成更新节点属性语句
        """
        properties_query = ""
        if method == "update":
            properties = dict(neo4j_node)
            for key in properties.keys():
                if key not in node.properties:
                    properties_query += f" SET n.`{key}`=Null "
        for key, value in node.properties.items():
            if isinstance(value, str):
                value = value.replace("'", "\\'")
                properties_query += f" SET n.`{key}`='{value}' "
            elif value is None:
                properties_query += f" SET n.`{key}`=Null "
            else:
                properties_query += f" SET n.`{key}`={value} "
        return properties_query

    def _set_update_node_label_query(self, neo4j_node: Neo4jNode, node: Node) -> str:
        """
        生成更新节点标签语句
        """
        set_labels_query = ""
        labels = [f"`{label}`" for label in neo4j_node.labels]
        if len(labels) > 0:
            set_labels_query += f" REMOVE n:{':'.join(labels)} "
        if len(node.labels) > 0:
            set_labels_query += (
                f" SET n:{':'.join([f'`{label}`' for label in node.labels])} "
            )
        return set_labels_query

    def _parse_edge(self, neo4j_edge: Neo4jEdge) -> Edge:
        """
        图数据库边转化为自定义边
        """
        properties = {}
        for key, value in neo4j_edge.items():
            properties[key] = value
        return Edge(
            self._parse_node(neo4j_edge.start_node),
            neo4j_edge.type,
            self._parse_node(neo4j_edge.end_node),
            neo4j_edge.id,
            properties,
        )

    def _parse_path(self, neo4j_path: Neo4jPath) -> Edge:
        """
        图数据库路径转化为自定义边
        """
        properties = {}
        neo4j_edge = neo4j_path.relationships[0]
        for key, value in neo4j_edge.items():
            properties[key] = value
        return Edge(
            self._parse_node(neo4j_edge.start_node),
            neo4j_edge.type,
            self._parse_node(neo4j_edge.end_node),
            neo4j_edge.id,
            properties,
        )

    def _get_edge(self, edge_id: int) -> Neo4jEdge:
        """
        获取图数据库节边
        """
        if edge_id is None:
            raise ValueError("edge is None.")
        if not isinstance(edge_id, int):
            raise ValueError("id not is int.")
        query = f"MATCH ()-[r]->() WHERE ID(r)={edge_id} RETURN r"
        with self.graph.session() as session:
            result = session.run(query)
            result = result.single()
            if result is None:
                raise ValueError("edge not found.")
            return result[0]

    def _get_path(self, edge_id: int) -> Neo4jPath:
        """
        获取图数据库节边
        """
        if edge_id is None:
            raise ValueError("edge is None.")
        if not isinstance(edge_id, int):
            raise ValueError("id not is int.")
        query = f"MATCH p=()-[r]->() WHERE ID(r)={edge_id} RETURN p"
        with self.graph.session() as session:
            result = session.run(query)
            result = result.single()
            if result is None:
                raise ValueError("edge not found.")
            return result[0]

    def _set_update_edge_property_query(
        self, method: str, neo4j_edge: Neo4jEdge, edge: Edge
    ) -> str:  # noqa
        """
        生成更新边属性语句
        """
        properties_query = ""
        if method == "update":
            properties = dict(neo4j_edge)
            for key in properties.keys():
                if key not in edge.properties:
                    properties_query += f" SET r.`{key}`=Null "
        for key, value in edge.properties.items():
            if isinstance(value, str):
                value = value.replace("'", "\\'")
                properties_query += f" SET r.`{key}`='{value}' "
            elif value is None:
                properties_query += f" SET r.`{key}`=Null "
            else:
                properties_query += f" SET r.`{key}`={value} "
        return properties_query

    def _get_edge_by_nodes_and_relation(
        self, neo4j_node1: Neo4jNode, neo4j_node2: Neo4jNode, relation: str
    ):
        """
        通过节点及边名查边
        """
        query = "MATCH (n1)-[r:`{0}`]->(n2) WHERE ID(n1)={1} AND ID(n2)={2} RETURN r".format(
            relation, neo4j_node1.id, neo4j_node2.id
        )
        with self.graph.session() as session:
            result = session.run(query)
            return result.single()

    def run(self, query) -> List:
        """执行自定义语句

        :param query: 自定义语句
        :return 数据对象列表
        :rtype: typing.List[]
        """
        with self.graph.session() as session:
            results = session.run(query)
            return list(results)

    def create_fulltext_index(
        self, index_name: str, labels: List[str], field_names: List[str]
    ) -> None:
        """创建全文索引

        :param index_name: 全文索引名称
        :param labels: 标签列表
        :param field_names: 索引字段名称列表
        :return None
        :rtype: None
        """
        label = '","'.join(labels)
        field_name = '","'.join(field_names)
        query = 'CALL db.index.fulltext.createNodeIndex("{0}",["{1}"],["{2}"])'.format(
            index_name, label, field_name
        )
        with self.graph.session() as session:
            session.run(query)

    def delete_fulltext_index(self, index_name: str) -> None:
        """删除全文索引

        :param index_name: 全文索引名称
        :return None
        :rtype: None
        """
        query = 'call db.index.fulltext.drop("{0}")'.format(index_name)
        with self.graph.session() as session:
            session.run(query)

    def search_nodes(
        self, index_name: str, query: str, skip: int = 0, limit: int = 20
    ) -> List[Node]:
        """使用全文索引搜索节点

        :param index_name: 全文索引名称
        :param query: 检索词
        :param skip: 偏移量
        :param limit: 返回数据量
        :return 节点列表
        :rtype: typing.List[models.Node]
        """
        query = 'CALL db.index.fulltext.queryNodes("{0}", "{1}") YIELD node,score RETURN node,score LIMIT {2}'.format(
            index_name, query, limit
        )
        with self.graph.session() as session:
            result = session.run(query)
            return [self._parse_node(entity["node"]) for entity in result]

    def get_node(self, id_: int) -> Node:
        """根据节点id获取节点

        :param id_: 节点ID
        :return 节点
        :rtype: models.Node
        """
        neo4j_node = self._get_node(id_)
        return self._parse_node(neo4j_node)

    def get_node_by_uuid(self, uuid: str, *labels) -> Node:
        """根据节点uuid获取节点

        :param uuid: 节点uuid
        :param *labels: 标签
        :return 节点
        :rtype: models.Node
        """
        if uuid is None:
            raise ValueError("uuid is None.")
        labels_: List[str] = [f"`{label}`" for label in labels]
        query = f"MATCH (n:{':'.join(labels_)}) WHERE n.`uuid`='{uuid}' or n.`_uuid`='{uuid}'  RETURN n"
        with self.graph.session() as session:
            result = session.run(query)
            result = result.single()
            if result is None:
                raise ValueError("node is None.")
            return self._parse_node(result[0])

    def get_nodes(
        self, skip: int = 0, limit: int = 20, *labels, **properties
    ) -> List[Node]:  # noqa
        """根据节点属性获取节点

        :param skip: 偏移量
        :param limit: 返回数据量
        :param *labels: 标签
        :param *properties: 属性
        :return 节点列表
        :rtype: typing.List[models.Node]
        """
        labels_: List[str] = [f"`{label}`" for label in labels]
        if len(properties) == 0:
            properties_query = ""
        else:
            properties_query = "WHERE "
            for property_, value in properties.items():
                if isinstance(value, str):
                    value = value.replace("'", "\\'")
                    properties_query += f" n.`{property_}`='{value}' AND"
                else:
                    properties_query += f" n.`{property_}`={value} AND"
        query = f"MATCH (n:{':'.join(labels_)}) {properties_query.rstrip('AND')} RETURN n SKIP {skip} LIMIT {limit}"
        with self.graph.session() as session:
            print(query)
            result = session.run(query)
            return [self._parse_node(neo4j_node[0]) for neo4j_node in result]

    def create_node(self, node: Node) -> Node:
        """创建节点(会重复创建节点)
        无论是否存在节点，均会创建

        :param node: 待新建节点
        :type node: models.Node
        :return: 创建好的新节点
        :rtype: models.Node
        """
        now_time = TimeUtils.now_str()
        node.properties["_create_time"] = now_time
        node.properties["_update_time"] = now_time
        node.properties["create_time"] = None
        node.properties["update_time"] = None
        return self._parse_node(self._create_node(*node.labels, **node.properties))

    def save_node(self, node: Node) -> Node:  # noqa
        """保存节点(不会重复创建节点)
        如果节点存在则更新节点，不存在则创建

        :param node: 待新建节点
        :type node: models.Node
        :return: 创建好的新节点或已有节点
        :rtype: models.Node
        """
        node_ = None
        if node.id:
            try:
                node_ = self.get_node(node.id)
            except ValueError:
                node_ = None
        if not node_:
            if node.properties.get("uuid") or node.properties.get("_uuid"):
                try:
                    uuid = node.properties.get("_uuid", node.properties["uuid"])
                    node_ = self.get_node_by_uuid(uuid, *node.labels)
                except ValueError:
                    node_ = None
            if not node_ and node.properties.get("name"):
                nodes = self.get_nodes(0, 20, *node.labels, name=node.name)
                if len(nodes) != 0:
                    node_ = nodes[0]
        if not node_:
            return self.create_node(node)
        node.id = node_.id
        return self.update_node(node)

    def update_node(self, node: Node) -> Node:
        """更新节点
        更新节点标签、属性，会整体替换

        :param node: 待更新节点
        :type node: models.Node
        :return: 更新好的新节点
        :rtype: models.Node
        """
        now_time = TimeUtils.now_str()
        neo4j_node = self._get_node(node.id)
        create_time = neo4j_node.get("_create_time")
        node.properties["_create_time"] = (
            create_time if create_time else neo4j_node["create_time"]
        )
        node.properties["_update_time"] = now_time
        node.properties["create_time"] = None
        node.properties["update_time"] = None
        logger.info(f"[UPDATE NODE] {node}")

        set_labels_query = self._set_update_node_label_query(neo4j_node, node)
        properties_query = self._set_update_node_property_query(
            "update", neo4j_node, node
        )
        query = f"MATCH (n) WHERE ID(n)={node.id} {set_labels_query} {properties_query} RETURN n"
        with self.graph.session() as session:
            result = session.run(query)
            return self._parse_node(result.single()[0])

    def partial_update_node(self, node: Node) -> Node:
        """部分更新节点
        只更新传入的数据，标签为没有则添加，属性为有则更新、没有则创建

        :param node: 待更新节点
        :type node: models.Node
        :return: 更新好的新节点
        :rtype: models.Node
        """
        now_time = TimeUtils.now_str()
        neo4j_node = self._get_node(node.id)
        create_time = neo4j_node.get("_create_time")
        node.properties["_create_time"] = (
            create_time if create_time else neo4j_node["create_time"]
        )
        node.properties["_update_time"] = now_time
        node.properties["create_time"] = None
        node.properties["update_time"] = None
        logger.info(f"[PARTIAL UPDATE NODE] {node}")

        set_labels_query = self._set_update_node_label_query(neo4j_node, node)
        properties_query = self._set_update_node_property_query(
            "partial_update", neo4j_node, node
        )
        query = f"MATCH (n) WHERE ID(n)={node.id} {set_labels_query} {properties_query} RETURN n"
        with self.graph.session() as session:
            result = session.run(query)
            return self._parse_node(result.single()[0])

    def delete_node(self, node: Node) -> None:
        """删除节点

        :param node: 待删除节点
        :type node: models.Node
        :return None
        :rtype: None
        """
        self._get_node(node.id)
        query = f"MATCH (n) WHERE ID(n)={node.id} DETACH DELETE n"
        with self.graph.session() as session:
            session.run(query)

    def get_edge(self, id_: int) -> Edge:
        """查询边

        :param id_: 边id
        :return 边
        :rtype: models.Edge
        """
        return self._parse_path(self._get_path(id_))

    def get_edges(
        self,
        node: Node,
        type_: EdgeTypes = EdgeTypes.ALL,
        skip: int = 0,
        limit: int = 20,
    ) -> List[Edge]:
        """查询节点边

        :param node: 节点
        :type node: models.Node
        :param type: 获取边类型，全部、入边、出边
        :param skip: 偏移量
        :param limit: 返回数据量
        :return 边列表
        :rtype: typing.List[models.Edge]
        """

        self._get_node(node.id)
        if type_ not in [EdgeTypes.ALL, EdgeTypes.IN, EdgeTypes.OUT]:
            raise ValueError("EdgeTypes not found.")
        if type_ == EdgeTypes.ALL:
            relation_query = "(n)-[r]-()"
        elif type_ == EdgeTypes.IN:
            relation_query = "()-[r]->(n)"
        elif type_ == EdgeTypes.OUT:
            relation_query = "(n)-[r]->()"
        query = f"MATCH p={relation_query} WHERE ID(n)={node.id} RETURN p SKIP {skip} LIMIT {limit}"
        with self.graph.session() as session:
            result = session.run(query)
            return [self._parse_path(neo4j_path[0]) for neo4j_path in result]

    def create_edge(self, edge: Edge) -> Edge:
        """创建边(会重复创建边)
        无论是否存在边，均会创建

        :param edge: 待创建边
        :type edge: models.Edge
        :return 创建好的边
        :rtype: models.Edge
        """
        neo4j_node1 = self._get_node(edge.src_node.id)
        neo4j_node2 = self._get_node(edge.dest_node.id)
        now_time = TimeUtils.now_str()
        properties = edge.properties
        properties["_create_time"] = now_time
        properties["_update_time"] = now_time
        properties["create_time"] = None
        properties["update_time"] = None
        relation_query = ""
        for property_, value in properties.items():
            if isinstance(value, str):
                value = value.replace("'", "\\'")
                relation_query += f" SET r.`{property_}`='{value}' "
            elif value is None:
                relation_query += f" SET r.`{property_}`=Null "
            else:
                relation_query += f" SET r.`{property_}`={value} "
        query = "match (n1), (n2) where id(n1)={0} and id(n2)={1} create p=(n1)-[r:`{2}`]->(n2) {3} return p".format(
            neo4j_node1.id, neo4j_node2.id, edge.relation, relation_query
        )
        with self.graph.session() as session:
            result = session.run(query)
            return self._parse_path(result.single()[0])

    def save_edge(self, edge: Edge) -> Edge:
        """保存边(不会重复创建边)
        如果边存在则更新边，不存在则创建

        :param edge: 待创建边
        :type edge: models.Edge
        :return 创建好的边或已存在的边
        :rtype: models.Edge
        """
        neo4j_node1 = self._get_node(edge.src_node.id)
        neo4j_node2 = self._get_node(edge.dest_node.id)
        now_time = TimeUtils.now_str()
        properties = edge.properties
        properties["_create_time"] = now_time
        properties["_update_time"] = now_time
        properties["create_time"] = None
        properties["update_time"] = None
        neo4j_edge = self._get_edge_by_nodes_and_relation(
            neo4j_node1, neo4j_node2, edge.relation
        )
        if neo4j_edge is not None:
            edge = self._parse_edge(neo4j_edge[0])
            edge.properties = properties
            return self.update_edge(edge)
        return self.create_edge(edge)

    def update_edge(self, edge: Edge) -> Edge:
        """更新边
        更新边属性，会整体替换

        :param edge: 待更新边
        :type edge: models.Edge
        :return: 更新好的边
        :rtype: models.Edge
        """
        now_time = TimeUtils.now_str()
        neo4j_edge = self._get_edge(edge.id)
        create_time = neo4j_edge.get("_create_time")
        edge.properties["_create_time"] = (
            create_time if create_time else neo4j_edge["create_time"]
        )
        edge.properties["_update_time"] = now_time
        edge.properties["create_time"] = None
        edge.properties["update_time"] = None

        properties_query = self._set_update_edge_property_query(
            "update", neo4j_edge, edge
        )
        query = f"MATCH p=()-[r]-() WHERE ID(r)={edge.id} {properties_query} RETURN p"
        with self.graph.session() as session:
            result = session.run(query)
            return self._parse_path(result.single()[0])

    def partial_update_edge(self, edge: Edge) -> Edge:
        """部分更新边
        只更新传入的数据，属性为有则更新、没有则创建

        :param edge: 待更新边
        :type edge: models.Edge
        :return: 更新好的边
        :rtype: models.Edge
        """
        now_time = TimeUtils.now_str()
        neo4j_edge = self._get_edge(edge.id)
        create_time = neo4j_edge.get("_create_time")
        edge.properties["_create_time"] = (
            create_time if create_time else neo4j_edge["create_time"]
        )
        edge.properties["_update_time"] = now_time
        edge.properties["create_time"] = None
        edge.properties["update_time"] = None

        properties_query = self._set_update_edge_property_query(
            "partial_update", neo4j_edge, edge
        )
        query = f"MATCH p=()-[r]-() WHERE ID(r)={edge.id} {properties_query} RETURN p"
        with self.graph.session() as session:
            result = session.run(query)
            return self._parse_path(result.single()[0])

    def delete_edge(self, edge: Edge) -> None:
        """删除边

        :param edge: 待删除边
        :type edge: models.Edge
        :return None
        :rtype: None
        """
        self._get_edge(edge.id)
        query = f"MATCH ()-[r]-() WHERE ID(r)={edge.id} DELETE r"
        with self.graph.session() as session:
            session.run(query)

    def count_nodes(self, labels: Optional[List[str]] = None) -> int:
        """统计节点数

        :param labels: 标签列表
        :type labels: List[str]
        :return: [节点数]
        :rtype: int
        """
        if labels is None:
            labels = []
        if not isinstance(labels, List):
            raise ValueError("labels must be list")
        labels_ = [f"`{label}`" for label in labels]
        labels_query = (":" if labels_ else "") + ":".join(labels_)
        cypher = f"MATCH (n{labels_query}) RETURN COUNT(n) AS count"
        with self.graph.session() as session:
            results = session.run(cypher).data()
            total: int = results[0]["count"] if results else 0
            return total

    def count_edges(
        self, labels: Optional[List[str]] = None, type_: EdgeTypes = EdgeTypes.ALL
    ) -> int:
        """统计边数

        :param labels: 标签列表
        :type labels: List[str]
        :param type_: 边类型, defaults to EdgeTypes.ALL
        :type type_: EdgeTypes, optional
        :return: 边数
        :rtype: int
        """
        if labels is None:
            labels = []
        if not isinstance(labels, List):
            raise ValueError("labels must be list")
        labels_ = [f"`{label}`" for label in labels]
        labels_query = (":" if labels_ else "") + ":".join(labels_)
        if type_ not in [EdgeTypes.ALL, EdgeTypes.IN, EdgeTypes.OUT]:
            raise ValueError("EdgeTypes not found.")
        type2cypher = {
            EdgeTypes.ALL: f"(n{labels_query})-[r]-()",
            EdgeTypes.IN: f"()-[r]->(n{labels_query})",
            EdgeTypes.OUT: f"(n{labels_query})-[r]->()",
        }
        relation_query = type2cypher[type_]
        cypher = f"MATCH {relation_query} RETURN COUNT(r) AS count"
        with self.graph.session() as session:
            results = session.run(cypher).data()
            total: int = results[0]["count"] if results else 0
            return total
