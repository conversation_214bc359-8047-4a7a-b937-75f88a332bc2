from py2neo import Graph
from py2neo.cypher import <PERSON><PERSON><PERSON>
from py2neo.errors import Neo4jError

from yunfu.db.graph.core.exceptions import Neo4jExceptionHandler
from yunfu.db.graph.core.logging import get_logger
from yunfu.db.graph.core.models.result import Result

from .base_client import BaseClient

logger = get_logger(__name__)


class Neo4jClient(BaseClient):

    def create_session(self) -> None:
        """创建session"""
        with self._lock:
            self._session = Graph(
                protocol=self.config.db.protocol,
                host=self.config.db.host,
                port=self.config.db.port,
                auth=(self.config.db.username, self.config.db.password),
                **self.config.db.kwargs,
            )

    def run(self, query: str, raise_exception: bool = True) -> Result:
        """执行query"""
        logger.info(f"query: {query[:200]}")
        assert self._session is not None  # mypy check
        try:
            cursor: Cursor = self._session.run(query)
            result = Result(
                query=query,
                data=cursor,
            )
        except Neo4jError as e:
            Neo4jExceptionHandler.handle(e, query, raise_exception)
            result = Result(query=query, code=-1, message=e.message, data=None)
        except Exception as e:
            logger.error(f"{query[:200]} => {e}")
            raise e
        return result
