import threading
from typing import Any, Dict, Optional

from yunfu.db.graph.core.models.config import Config
from yunfu.db.graph.core.models.result import Result

Session = Any


class BaseClient:
    """图数据库客户端基类"""
    _lock = threading.Lock()
    _instances: Dict[int, "BaseClient"] = {}
    _session: Optional[Session] = None

    def __new__(cls, config: Config) -> "BaseClient":
        with cls._lock:
            key = hash(config.db)
            if key not in cls._instances:
                cls._instances[key] = super().__new__(cls)
        return cls._instances[key]

    def __init__(self, config: Config) -> None:
        self.config = config
        self.create_session()

    def create_session(self) -> None:
        """创建session"""
        raise NotImplementedError

    def run(self, query: str, raise_exception: bool = True) -> Result:
        """执行query"""
        raise NotImplementedError
