import time

from nebula3.common.ttypes import ErrorCode
from nebula3.Config import Config as NebulaConfig
from nebula3.data.ResultSet import ResultSet
from nebula3.gclient.net import ConnectionPool

from yunfu.db.graph.core.exceptions import NebulaExceptionHandler
from yunfu.db.graph.core.logging import get_logger
from yunfu.db.graph.core.models.config import Config
from yunfu.db.graph.core.models.result import Result

from .base_client import BaseClient

logger = get_logger(__name__)


class NebulaClient(BaseClient):
    """nebula图数据库客户端"""
    _pool = ConnectionPool()

    def __init__(self, config: Config) -> None:
        nebula_config = NebulaConfig()
        for key, value in config.db.kwargs.items():
            if hasattr(nebula_config, key):
                setattr(nebula_config, key, value)
        self._pool.init([(config.db.host, config.db.port)], nebula_config)
        super().__init__(config)
        self.wait_secs = config.db.heartbeat_interval_secs * 2

    def create_session(self) -> None:
        """创建session"""
        with self._lock:
            self._session = self._pool.get_session(
                self.config.db.username, self.config.db.password
            )

    def run(self, query: str, raise_exception: bool = True) -> Result:
        """执行query"""
        logger.info(f"query: {query[:200]}")
        result_set = self.get_result_set(query, raise_exception)
        return Result(
            query=query,
            code=result_set.error_code(),
            message=result_set.error_msg(),
            data=result_set,
        )

    def get_result_set(self, query: str, raise_exception: bool = True) -> ResultSet:
        """获取nebula原始结果"""
        assert self._session is not None  # mypy check
        result_set = self._session.execute(query)
        if result_set.error_code() in [
            ErrorCode.E_SESSION_INVALID,
            ErrorCode.E_SESSION_TIMEOUT,
        ]:
            self.create_session()
            result_set = self._session.execute(query)
        NebulaExceptionHandler.handle(result_set, query, raise_exception)
        return result_set

    def wait(self) -> None:
        """等待(某些操作异步执行，需要等待结果)"""
        time.sleep(self.wait_secs)
