from typing import Union

from nebula3.data.ResultSet import ResultSet
from py2neo.cypher import Cursor
from pydantic import BaseModel


class Result(BaseModel):
    """结果类
    Attributes:
        query: 执行的查询语句
        code: 返回码，0表示成功，非0表示失败
        message: 错误信息
        data: 查询结果集
    """
    query: str
    code: int = 0
    message: str = ""
    data: Union[ResultSet, Cursor, None]

    __annotations__ = {
        "query": str,
        "code": int,
        "message": str,
        "data": Union[ResultSet, Cursor, None],
    }

    @property
    def is_succeeded(self) -> bool:
        return self.code == 0

    class Config:
        arbitrary_types_allowed = True
