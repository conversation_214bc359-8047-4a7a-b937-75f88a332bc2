from typing import Any, Dict

from pydantic import BaseModel
from pydantic.config import ConfigDict


class DbConfig(BaseModel):
    """数据库配置"""

    host: str  # 数据库地址
    port: int  # 数据库端口
    username: str  # 用户名
    password: str  # 密码
    protocol: str = "bolt"  # 协议
    kwargs: Dict[str, Any] = {}  # 其他非必须参数
    model_config = ConfigDict(frozen=True)

    __annotations__ = {
        "host": str,
        "port": int,
        "username": str,
        "password": str,
        "protocol": str,
        "kwargs": Dict[str, Any],
    }

    def __hash__(self) -> int:
        return hash(self.model_dump_json(exclude={"kwargs"}))

    @property
    def heartbeat_interval_secs(self) -> int:
        heartbeat_interval_secs = self.kwargs.get("heartbeat_interval_secs", 10)
        assert isinstance(heartbeat_interval_secs, int)  # mypy check
        return min(heartbeat_interval_secs, 10)

    @property
    def timeout(self) -> int:
        timeout = self.kwargs.get("timeout", 30)
        assert isinstance(timeout, int)
        return min(timeout, 30)


class VersionConfig(BaseModel):
    """版本配置"""

    enabled: bool = False  # 是否启用版本
    c: str = "c"  # 当前版本标识符
    model_config = ConfigDict(frozen=True)

    __annotations__ = {
        "enabled": bool,
        "c": str,
    }


class Config(BaseModel):
    """配置"""

    db: DbConfig
    version: VersionConfig = VersionConfig()
    model_config = ConfigDict(frozen=True)

    __annotations__ = {
        "db": DbConfig,
        "version": VersionConfig,
    }
