from nebula3.data.ResultSet import ResultSet
from py2neo.errors import Neo4jError

from yunfu.db.graph.core.logging import get_logger

logger = get_logger(__name__)


class QueryExecuteError(Exception):

    def __init__(self, code: int, message: str, query: str) -> None:
        self.code = code
        self.message = message
        self.query = query

    def __str__(self) -> str:
        return f"Query Execute Error: {self.query[:200]} => {self.code} {self.message} "


class NotExistedError(Exception):

    def __init__(self, key: str, value: str) -> None:
        self.key = key
        self.value = value

    def __str__(self) -> str:
        return f"{self.key} Not Existed: {self.value}"


class NebulaExceptionHandler:

    @staticmethod
    def handle(result: ResultSet, query: str, raise_exception: bool = True) -> None:
        if not result.is_succeeded() and raise_exception:
            code, message = result.error_code(), result.error_msg()
            logger.error(f"{query[:200]} => {code} {message} ")
            raise QueryExecuteError(code, message, query)


class Neo4jExceptionHandler:

    @staticmethod
    def handle(error: Neo4jError, query: str, raise_exception: bool = True) -> None:
        logger.error(f"{query[:200]} => {error.code} {error.message} ")
        if raise_exception:
            if error.code == "Neo.ClientError.Statement.SyntaxError":
                raise QueryExecuteError(error.code, error.message, query) from None
            raise error
