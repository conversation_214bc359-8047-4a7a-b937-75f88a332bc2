from typing import List, Optional

from pydantic import BaseModel

from yunfu.db.graph.core.constants import SPACE_PREFIX
from yunfu.db.graph.core.enums import BaseEnum

from .node import NodeProp


class Space(BaseModel):
    """图空间"""

    name: str
    props: List[NodeProp] = []  # 属性列表，Nebula专用

    __annotations__ = {
        "name": str,
        "props": List[NodeProp],
    }

    @staticmethod
    def check_name(name: str) -> bool:
        return name.startswith(SPACE_PREFIX)


class NebulaSpace(BaseModel):
    """图空间"""

    name: str
    partition_num: int = 10
    replica_factor: int = 1
    node_id_type: str = "FIXED_STRING(22)"
    comment: str = ""

    __annotations__ = {
        "name": str,
        "partition_num": int,
        "replica_factor": int,
        "node_id_type": str,
        "comment": str,
    }


class IndexTypes(BaseEnum):
    """索引类型"""

    NEBULA_TAG = "TAG"
    NEBULA_EDGE = "EDGE"


class Index(BaseModel):
    """索引"""

    name: str
    related_to: str
    type: IndexTypes
    props: List[str] = []
    comment: Optional[str] = None

    __annotations__ = {
        "name": str,
        "related_to": str,
        "type": IndexTypes,
        "props": List[str],
        "comment": Optional[str],
    }


class Constraint(BaseModel):
    """约束"""

    name: str

    __annotations__ = {
        "name": str,
    }


class Version(BaseModel):
    name: str = "c"

    __annotations__ = {
        "name": str,
    }
