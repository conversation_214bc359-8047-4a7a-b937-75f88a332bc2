import time
import uuid
from typing import Any, ClassVar, Dict, List, Optional

from pydantic import BaseModel, field_validator, model_validator

from yunfu.common import yfid
from yunfu.db.graph.core.enums import Databases


class NodeProp(BaseModel):
    """节点属性"""

    name: str  # 属性名
    type: str  # 属性类型
    nullable: bool = True  # 是否可空
    default: Optional[Any] = None  # 默认值
    comment: Optional[str] = None  # 描述

    __annotations__ = {
        "name": str,
        "type": str,
        "nullable": bool,
        "default": Optional[Any],
        "comment": Optional[str],
        "check_type": ClassVar
    }

    def check_type(self, db: Databases) -> bool:
        # TODO 检查属性类型是否合法
        return True


class NodeType(BaseModel):
    """节点类型"""

    name: str  # 类型名
    props: List[NodeProp] = []  # 属性列表
    comment: Optional[str] = None  # 描述
    ttl_duration: int = 0  # 过期时间
    ttl_col: Optional[str] = None  # 过期属性

    __annotations__ = {
        "name": str,
        "props": List[NodeProp],
        "comment": Optional[str],
        "ttl_duration": int,
        "ttl_col": Optional[str],
    }


class Node(BaseModel):
    """节点"""

    id: Optional[str] = None
    types: List[str] = []
    props: Dict[str, Any] = {}

    __annotations__ = {
        "id": str,
        "types": List[str],
        "props": Dict[str, Any],
    }

    @model_validator(mode="before")
    @classmethod
    def set_id(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        if not values.get("id"):
            values["id"] = cls.get_id(values.get("props", {}).get("name", ""))
        return values

    @field_validator("props")
    @classmethod
    def check_props(cls, props: Dict[str, Any]) -> Dict[str, Any]:
        props.pop("_id", None)  # Neo4j 用 _id 属性存id
        return props

    @property
    def name(self):
        return self.props.get("name")

    @staticmethod
    def get_id(name: str) -> str:
        now = time.time()
        return yfid(str(uuid.uuid3(uuid.NAMESPACE_DNS, f"{name}_{now}")))
