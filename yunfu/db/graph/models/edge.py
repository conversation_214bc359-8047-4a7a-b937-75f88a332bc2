import time
import uuid
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, model_validator

from yunfu.common import yfid

from .node import Node


class EdgeProp(BaseModel):
    """节点属性"""

    name: str  # 属性名
    type: str  # 属性类型
    nullable: bool = True  # 是否可空
    default: Optional[Any] = None  # 默认值
    comment: Optional[str] = None  # 描述

    __annotations__ = {
        "name": str,
        "type": str,
        "nullable": bool,
        "default": Optional[Any],
        "comment": Optional[str],
    }


class EdgeType(BaseModel):
    """节点类型"""

    name: str  # 类型名
    props: List[EdgeProp] = []  # 属性列表
    comment: Optional[str] = None  # 描述
    ttl_duration: int = 0  # 过期时间
    ttl_col: Optional[str] = None  # 过期属性

    __annotations__ = {
        "name": str,
        "props": List[EdgeProp],
        "comment": Optional[str],
        "ttl_duration": int,
        "ttl_col": Optional[str],
    }


class Edge(BaseModel):
    """边"""

    id: Optional[str] = None
    src_id: str
    dst_id: str
    type: str
    props: Dict[str, Any] = {}
    rank: int = 0
    src_node: Optional[Node] = None
    dst_node: Optional[Node] = None

    __annotations__ = {
        "id": str,
        "src_id": str,
        "dst_id": str,
        "type": str,
        "props": Dict[str, Any],
        "rank": int,
        "src_node": Optional[Node],
        "dst_node": Optional[Node],
    }

    @model_validator(mode="before")
    @classmethod
    def set_id(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        if not values.get("id"):
            values["id"] = cls.get_id(values.get("props", {}).get("name", ""))
        return values

    @staticmethod
    def get_id(name: str) -> str:
        now = time.time()
        return yfid(str(uuid.uuid3(uuid.NAMESPACE_DNS, f"{name}_{now}")))
