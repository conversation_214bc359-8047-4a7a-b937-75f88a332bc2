from typing import List

from pydantic import BaseModel

from .edge import Edge
from .node import Node


class Segment(BaseModel):
    start: Node
    end: Node
    edge: Edge

    __annotations__ = {
        "start": Node,
        "end": Node,
        "edge": Edge,
    }


class Path(BaseModel):
    nodes: List[Node]
    edges: List[Edge]
    segments: List

    __annotations__ = {
        "nodes": List[Node],
        "edges": List[Edge],
        "segments": List[Segment],
    }

    @property
    def start_node(self):
        if len(self.nodes) == 0:
            return None
        return self.nodes[0]

    @property
    def end_node(self):
        if len(self.nodes) == 0:
            return None
        return self.nodes[-1]

    @property
    def length(self):
        return len(self.segments)
