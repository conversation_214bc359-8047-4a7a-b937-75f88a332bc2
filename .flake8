[flake8]
select = B,C,E,F,P,T4,W,B9,N,C4,T0,A0,N4,CF,R,SIM,H,VNE,ECE,CCR,A5,AAA,ANN
ignore =  A002, A003, ANN001, ANN101, ANN102, ANN201, ANN204, ANN401, VNE001, VNE003, AAA01, AAA05, PT012, E999, C400, C416, E800, ECE001, B903, B024, B027, C901, CCR001,W503,W504,ANN206,VNE002,ABB001,H238,H301,H304,H306,H401,H404,H405,Q000,E704
exclude = env/*, scripts/*, tests/*
max-line-length = 120
max-complexity = 12
max-expression-complexity = 6
max-cognitive-complexity = 30
